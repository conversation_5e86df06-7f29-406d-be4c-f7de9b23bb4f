package one.srp.core.network.clients

import com.jakewharton.retrofit2.converter.kotlinx.serialization.asConverterFactory
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import one.srp.core.network.clients.converter.NullOnEmptyConverterFactory
import one.srp.core.network.utils.JSON
import retrofit2.Retrofit

const val METRIC_BASE_URL = "https://t.iminsp.com"

fun createMetricRetrofit(client: OkHttpClient): Retrofit {
    return Retrofit.Builder().apply {
        baseUrl(METRIC_BASE_URL)
        client(client)

        addConverterFactory(JSON.asConverterFactory("application/json".toMediaType()))
    }.build()
}

fun createServiceRetrofit(client: OkHttpClient, baseUrl: String): Retrofit {
    return Retrofit.Builder().apply {
        client(client)
        baseUrl(baseUrl)

        addConverterFactory(NullOnEmptyConverterFactory())
        addConverterFactory(JSON.asConverterFactory("application/json".toMediaType()))
    }.build()
}