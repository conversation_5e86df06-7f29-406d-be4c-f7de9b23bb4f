package one.srp.gensmo.utils.integration

import android.content.Context
import androidx.core.content.edit
import androidx.core.net.toUri
import com.onesignal.OneSignal
import com.onesignal.notifications.INotificationClickListener
import com.onesignal.notifications.INotificationClickEvent
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import one.srp.core.analytics.events.SelectItem
import one.srp.core.analytics.types.EventItem
import one.srp.core.analytics.types.EventItemCategory
import one.srp.core.analytics.types.EventItemListName
import one.srp.core.analytics.types.EventMethod
import one.srp.core.analytics.types.EventRefer
import one.srp.gensmo.utils.metrics.MetricManager
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class OneSignalManager @Inject constructor(
    private val metricManager: MetricManager
) {

    private val clickListener = object : INotificationClickListener {
        override fun onClick(event: INotificationClickEvent) {
            Timber.d("INotificationClickListener.onClick fired with event: $event")
            Timber.d("Notification: ${event.notification}")
            Timber.d("Notification Title: ${event.notification.title}")
            Timber.d("Additional Data: ${event.notification.additionalData}")

            // 处理 schema 跳转 - 优先使用 launchURL，其次使用 additionalData 中的 schema
            val targetUrl = event.notification.launchURL?.takeIf { it != "null" && it.isNotBlank() }
                ?: event.notification.additionalData?.get("schema")?.toString()
            
            targetUrl?.let { url ->
                try {
                    val uri = url.toUri()
                    
                    // 上报通知点击事件
                    metricManager.log(
                        SelectItem(
                            refer = EventRefer.App,
                            itemListName = EventItemListName.SystemNotification,
                            method = EventMethod.Click,
                            items = listOf(
                                EventItem(
                                    itemCategory = EventItemCategory.Notification,
                                    itemName = url
                                )
                            )
                        )
                    )
                    
                    NavManager.navigateDeepLink(uri)
                } catch (e: Exception) {
                    Timber.e(e, "Failed to parse URL: $url")
                }
            }
        }
    }

    fun initialize(context: Context) {
        // OneSignal initialization
        OneSignal.initWithContext(context, "************************************")

        // 只在首次启动时请求通知权限
        val sharedPref = context.getSharedPreferences("onesignal_prefs", Context.MODE_PRIVATE)
        val hasRequestedPermission = sharedPref.getBoolean("notification_permission_requested", false)
        
        if (!hasRequestedPermission) {
            CoroutineScope(Dispatchers.IO).launch {
                OneSignal.Notifications.requestPermission(true)
                // 记录已经请求过权限
                sharedPref.edit {
                    putBoolean("notification_permission_requested", true)
                }
            }
        }

        OneSignal.Notifications.addClickListener(clickListener)
    }

    fun registerUser(userId: String) {
        OneSignal.login(userId)
    }

    fun cleanup() {
        OneSignal.Notifications.removeClickListener(clickListener)
    }
}
