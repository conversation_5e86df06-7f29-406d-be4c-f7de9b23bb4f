package one.srp.gensmo.data.model

import kotlinx.serialization.Serializable

@Serializable
data class MainImage(
    val link: String? = null,
)

@Serializable
data class Price(
    val raw: String? = null,
)

@Serializable
data class BuyboxWinner(
    val price: Price? = null,
)

@Serializable
data class Tags(
    val cateTag: String? = null,
)

@Serializable
data class ProductItem(
    val globalId: String = "",
    val id: String = "",
    val title: String? = null,
    val link: String? = null,
    val searchEngine: String? = null,
    val platform: String? = null,
    val source: String? = null,
    val sourceIcon: String? = null,
    val mainImage: MainImage? = null,
    val buyboxWinner: BuyboxWinner? = null,
    val tags: Tags? = null,
    val brand: String? = null,
    val brandParentName: String? = null,

    var isFavorited: Boolean? = null,
)