package one.srp.gensmo.data.repository

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.launch
import one.srp.gensmo.data.remote.NotificationService
import one.srp.gensmo.data.remote.apis.NotificationItem
import one.srp.gensmo.data.remote.apis.MarkNotificationReadRequest
import one.srp.gensmo.data.remote.apis.Pagination
import one.srp.gensmo.data.store.UserDataStoreManager
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class NotificationRepository @Inject constructor() {

    private val coroutineScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)

    data class NotificationState(
        val isLoading: Boolean = false,
        val isLoadingMore: Boolean = false,
        val error: String? = null,
        val notifications: List<NotificationItem> = emptyList(),
        val pagination: Pagination? = null,
        val hasMore: Boolean = true,
        val unreadCount: Int = 0
    )

    private val _state = MutableStateFlow(NotificationState())
    val state: StateFlow<NotificationState> = _state.asStateFlow()

    // 只读的未读数量，供外部使用
    val unreadCount: StateFlow<Int> = _state.asStateFlow().map { it.unreadCount }.stateIn(
        scope = coroutineScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = 0
    )

    private var currentPage = 1
    private val pageSize = 20

    init {
        // 初始化时不立即加载通知，等待用户登录后再加载
        checkAndLoadNotificationsIfNeeded()
        
        // 注册登录状态变化回调
        UserDataStoreManager.registerLoginStateChangeCallback {
            coroutineScope.launch {
                val isLoggedIn = UserDataStoreManager.isUserLoggedIn()
                if (isLoggedIn) {
                    Timber.d("收到登录状态变化通知，用户已登录，刷新通知数据")
                    refresh()
                } else {
                    Timber.d("收到登录状态变化通知，用户已登出，清空通知数据")
                    _state.value = _state.value.copy(
                        notifications = emptyList(),
                        unreadCount = 0,
                        error = null
                    )
                }
            }
        }
    }

    /**
     * 检查用户登录状态，如果已登录则加载通知
     */
    private fun checkAndLoadNotificationsIfNeeded() {
        coroutineScope.launch {
            val isLoggedIn = UserDataStoreManager.isUserLoggedIn()
            if (isLoggedIn) {
                loadNotifications()
            } else {
                Timber.d("用户未登录，跳过通知加载")
            }
        }
    }

    /**
     * 加载通知列表
     * @param refresh 是否为刷新操作，如果是则重置页码
     */
    fun loadNotifications(refresh: Boolean = false) {
        coroutineScope.launch {
            if (refresh) {
                currentPage = 1
            }

            try {
                // 如果是刷新，显示主加载状态；否则显示加载更多状态
                if (refresh || currentPage == 1) {
                    _state.value = _state.value.copy(
                        isLoading = true,
                        error = null
                    )
                } else {
                    _state.value = _state.value.copy(
                        isLoadingMore = true,
                        error = null
                    )
                }

                val response = NotificationService.api.getNotificationList(
                    page = currentPage,
                    pageSize = pageSize
                )

                if (response.isSuccessful) {
                    val body = response.body()
                    if (body != null) {
                        val newNotifications = if (refresh || currentPage == 1) {
                            body.data
                        } else {
                            _state.value.notifications + body.data
                        }

                        val unreadCount = newNotifications.count { it.status == "unread" }

                        _state.value = _state.value.copy(
                            isLoading = false,
                            isLoadingMore = false,
                            notifications = newNotifications,
                            pagination = body.pagination,
                            hasMore = currentPage < body.pagination.totalPages,
                            unreadCount = unreadCount,
                            error = null
                        )

                        if (!refresh && body.data.isNotEmpty()) {
                            currentPage++
                        } else if (refresh) {
                            currentPage = 2 // 下次加载第二页
                        }

                        Timber.d("通知列表加载成功: ${body.data.size} 条通知，未读: $unreadCount")
                    } else {
                        _state.value = _state.value.copy(
                            isLoading = false,
                            isLoadingMore = false,
                            error = "数据为空"
                        )
                    }
                } else {
                    _state.value = _state.value.copy(
                        isLoading = false,
                        isLoadingMore = false,
                        error = "加载失败: ${response.message()}"
                    )
                    Timber.e("加载通知列表失败: ${response.message()}")
                }
            } catch (e: Exception) {
                _state.value = _state.value.copy(
                    isLoading = false,
                    isLoadingMore = false,
                    error = "网络错误: ${e.message}"
                )
                Timber.e(e, "加载通知列表发生异常")
            }
        }
    }

    /**
     * 刷新通知列表
     */
    fun refresh() {
        loadNotifications(refresh = true)
    }

    /**
     * 加载更多通知
     */
    fun loadMore() {
        if (_state.value.hasMore && !_state.value.isLoadingMore && !_state.value.isLoading) {
            loadNotifications(refresh = false)
        }
    }

    /**
     * 清除错误状态
     */
    fun clearError() {
        _state.value = _state.value.copy(error = null)
    }

    /**
     * 获取所有未读通知的ID
     */
    private fun getUnreadNotificationIds(): List<String> {
        return _state.value.notifications
            .filter { it.status == "unread" }
            .map { it.notificationId }
    }

    /**
     * 标记所有未读通知为已读
     */
    fun markAllNotificationsAsRead() {
        coroutineScope.launch {
            val unreadIds = getUnreadNotificationIds()
            if (unreadIds.isNotEmpty()) {
                markNotificationsAsRead(unreadIds)
            }
        }
    }

    /**
     * 标记通知为已读
     * @param notificationIds 通知ID列表
     */
    fun markNotificationsAsRead(notificationIds: List<String>) {
        coroutineScope.launch {
            try {
                val request = MarkNotificationReadRequest(notificationIds)
                val response = NotificationService.api.readNotification(request)
                
                if (response.isSuccessful) {
                    // 更新本地通知状态为已读
                    val updatedNotifications = _state.value.notifications.map { notification ->
                        if (notificationIds.contains(notification.notificationId)) {
                            notification.copy(status = "read")
                        } else {
                            notification
                        }
                    }
                    
                    val unreadCount = updatedNotifications.count { it.status == "unread" }
                    
                    _state.value = _state.value.copy(
                        notifications = updatedNotifications,
                        unreadCount = unreadCount
                    )
                    
                    Timber.d("标记通知为已读成功: ${notificationIds.size} 条通知，剩余未读: $unreadCount")
                } else {
                    Timber.e("标记通知为已读失败: ${response.message()}")
                }
            } catch (e: Exception) {
                Timber.e(e, "标记通知为已读发生异常")
            }
        }
    }

    /**
     * 获取未读通知数量
     */
    fun getUnreadCount(): Int {
        return _state.value.unreadCount
    }
}
