package one.srp.gensmo.data.remote.paging

import androidx.paging.PagingSource
import androidx.paging.PagingState
import one.srp.gensmo.data.model.FeedItem
import one.srp.gensmo.data.remote.FeedService
import one.srp.gensmo.data.store.DeviceDataStoreManager
import one.srp.gensmo.data.store.UserDataStoreManager
import timber.log.Timber

class SimilarFeedPagingSource(
    private val itemId: String
) : PagingSource<Int, FeedItem>() {
    
    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, FeedItem> {
        return try {
            val offset = params.key ?: 0
            val limit = params.loadSize
            
            // 在suspend函数内部获取用户ID和设备ID
            val uid = UserDataStoreManager.getUserId()
            val deviceId = DeviceDataStoreManager.getDeviceId()
            
            Timber.d("Loading similar items: offset=$offset, limit=$limit, itemId=$itemId")
            
            val response = FeedService.api.getSimilarFeed(
                limit = limit,
                offset = offset,
                tab = "similar",
                itemId = itemId,
                uid = uid,
                deviceId = deviceId
            )
            
            if (response.isSuccessful) {
                val feedRes = response.body()
                val data = feedRes?.data ?: emptyList()
                val hasMore = feedRes?.hasMore == true
                
                Timber.d("Similar items loaded: ${data.size} items, hasMore=$hasMore")
                
                val nextKey = if (hasMore && data.isNotEmpty()) offset + data.size else null
                
                LoadResult.Page(
                    data = data,
                    prevKey = if (offset == 0) null else offset - limit,
                    nextKey = nextKey
                )
            } else {
                Timber.e("API error: ${response.code()} - ${response.errorBody()?.string()}")
                LoadResult.Error(Exception("API error: ${response.code()}"))
            }
        } catch (e: Exception) {
            Timber.e(e, "Error loading similar items")
            LoadResult.Error(e)
        }
    }

    override fun getRefreshKey(state: PagingState<Int, FeedItem>): Int? {
        return state.anchorPosition?.let { anchorPosition ->
            val anchorPage = state.closestPageToPosition(anchorPosition)
            anchorPage?.prevKey?.plus(1) ?: anchorPage?.nextKey?.minus(1)
        }
    }
} 