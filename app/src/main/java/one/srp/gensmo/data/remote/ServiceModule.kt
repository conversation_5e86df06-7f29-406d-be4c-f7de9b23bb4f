package one.srp.gensmo.data.remote

import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import one.srp.gensmo.data.remote.apis.AbApi
import one.srp.gensmo.data.remote.apis.CommunityApi
import one.srp.gensmo.data.remote.apis.MiscApi
import one.srp.gensmo.data.remote.apis.SessionApi
import one.srp.gensmo.data.remote.apis.TryOnApi
import one.srp.gensmo.data.remote.apis.UserApi
import one.srp.gensmo.data.remote.apis.NotificationApi
import one.srp.gensmo.data.remote.utils.RemoteClients
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object ServiceModule {
    @Provides
    @Singleton
    fun provideMiscService(): MiscApi {
        return RemoteClients.workflow.create(MiscApi::class.java)
    }

    @Provides
    @Singleton
    fun provideTryOnService(): TryOnApi {
        return RemoteClients.workflow.create(TryOnApi::class.java)
    }

    @Provides
    @Singleton
    fun provideUserService(): UserApi {
        return RemoteClients.workflow.create(UserApi::class.java)
    }

     @Provides
    @Singleton
    fun provideNotificationService(): NotificationApi {
        return RemoteClients.workflow.create(NotificationApi::class.java)
    }

    @Provides
    @Singleton
    fun provideChatService(): SessionApi {
        return RemoteClients.workflow.create(SessionApi::class.java)
    }

    @Provides
    @Singleton
    fun provideABService(): AbApi {
        return RemoteClients.account.create(AbApi::class.java)
    }

    @Provides
    @Singleton
    fun provideCommunityService(): CommunityApi {
        return RemoteClients.workflow.create(CommunityApi::class.java)
    }
}