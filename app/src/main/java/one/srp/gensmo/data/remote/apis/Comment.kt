package one.srp.gensmo.data.remote.apis

import one.srp.gensmo.data.model.CommentListRes
import one.srp.gensmo.data.model.PostCommentReq
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Path

const val BASE_PATH = "/gem-user-comment"

interface CommentApi {
    @GET("$BASE_PATH/posts/{post_id}/comments")
    suspend fun getPostComments(
        @Path("post_id") postId: String,
    ): Response<CommentListRes>

    @POST("$BASE_PATH/comments")
    suspend fun postComment(
        @Body body: PostCommentReq,
    ): Response<Unit>
}
