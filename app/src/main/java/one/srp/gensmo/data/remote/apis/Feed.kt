package one.srp.gensmo.data.remote.apis

import kotlinx.serialization.Serializable
import one.srp.gensmo.data.model.FeedItem
import one.srp.gensmo.data.remote.FeedTabType
import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.Query

@Serializable
data class FeedRes(
    val data: List<FeedItem>,
    val hasMore: Boolean,
    val lastUpdateTime: String? = null,
    val traceId: String? = null,
)

@Serializable
data class FeedDetailRes(
    val data: FeedItem,
)

interface FeedApi {
    @GET("/favie_gem_workflow/recommend")
    suspend fun getFeedList(
        @Query("uid") uid: String?,
        @Query("device_id") deviceId: String?,
        @Query("tab") tab: FeedTabType,
        @Query("offset") offset: Int,
        @Query("limit") limit: Int,
        @Query("last_update_time") lastUpdateTime: String? = null,
    ): Response<FeedRes>

    @GET("/favie_gem_workflow/feed/detail")
    suspend fun getFeedDetail(
        @Query("feed_type") feedType: String,
        @Query("feed_id") feedId: String,
        @Query("user_id") userId: String? = null,
    ): Response<FeedDetailRes>

    @GET("/favie_gem_workflow/recommend_hashtags")
    suspend fun getRecommendHashtags(
        @Query("hashtags") hashtags: String?,
        @Query("offset") offset: Int,
        @Query("limit") limit: Int
    ): Response<FeedRes>

    @GET("/favie_gem_workflow/similar")
    suspend fun getSimilarFeed(
        @Query("limit") limit: Int = 10,
        @Query("offset") offset: Int = 0,
        @Query("tab") tab: String = "similar",
        @Query("item_id") itemId: String,
        @Query("uid") uid: String?,
        @Query("device_id") deviceId: String?,
    ): Response<FeedRes>
}
