package one.srp.gensmo.data.remote.apis

import kotlinx.serialization.Serializable
import one.srp.gensmo.data.model.FollowersResponse
import retrofit2.Response
import retrofit2.http.DELETE
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query

interface CommunityApi {
    /**
     * 关注用户
     * POST /community/follow
     */
    @POST("/community/follow")
    suspend fun followUser(
        @Query("followed_id") followedId: String,
    ): Response<FollowRes>

    /**
     * 取消关注用户
     * DELETE /community/follow
     */
    @DELETE("/community/follow")
    suspend fun unfollowUser(
        @Query("followed_id") followedId: String,
    ): Response<FollowRes>

    /**
     * 检查关注状态
     * GET /community/follow/check
     */
    @GET("/community/follow/check")
    suspend fun checkFollowStatus(
        @Query("followed_id") followedId: String,
    ): Response<FollowRes>

    /**
     * 获取followers列表
     * GET /community/followers/list
     */
    @GET("/community/followers/list")
    suspend fun getFollowersList(
        @Query("user_id") userId: String,
        @Query("page") page: Int,
        @Query("page_size") pageSize: Int,
    ): Response<FollowersResponse>

    /**
     * 获取following列表
     * GET /community/following/list
     */
    @GET("/community/following/list")
    suspend fun getFollowingList(
        @Query("user_id") userId: String,
        @Query("page") page: Int,
        @Query("page_size") pageSize: Int,
    ): Response<FollowersResponse>
}

@Serializable
data class FollowRes(
    val status: String? = null,
)
