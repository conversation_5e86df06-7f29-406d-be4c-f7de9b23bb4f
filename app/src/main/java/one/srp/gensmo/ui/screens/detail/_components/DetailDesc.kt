package one.srp.gensmo.ui.screens.detail._components

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import one.srp.core.analytics.events.SelectItem
import one.srp.core.analytics.types.EventActionType
import one.srp.core.analytics.types.EventItem
import one.srp.core.analytics.types.EventItemCategory
import one.srp.core.analytics.types.EventItemListName
import one.srp.core.analytics.types.EventMethod
import one.srp.core.analytics.types.EventRefer
import one.srp.gensmo.R
import one.srp.gensmo.data.model.FeedItem
import one.srp.gensmo.ui.navigation.NavActions
import one.srp.gensmo.ui.screens.tryon.create._components.LoginModal
import one.srp.gensmo.ui.theme.AppThemeColors
import one.srp.gensmo.ui.theme.AppThemeTextStyle
import one.srp.gensmo.utils.metrics.MetricViewModel
import one.srp.gensmo.viewmodel.tryon.CreateViewModel
import timber.log.Timber
import java.time.LocalDateTime
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter
import kotlin.time.ExperimentalTime

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun DetailDesc(
    modifier: Modifier = Modifier,
    item: FeedItem,
    liked: Boolean = false,
    onLikeClick: () -> Unit = {},
    sharedCount: Int = 0,
    onShareClick: () -> Unit = {},
    saved: Boolean = false,
    onSaveClick: () -> Unit = {},
    onCommentClick: () -> Unit = {},
    navActions: NavActions = NavActions(),
) {
    // Metric helper for feed detail
    val metricViewModel: MetricViewModel = hiltViewModel()
    val metric = metricViewModel.compatMetric(EventRefer.FeedDetail)

    val context = LocalContext.current
    val createViewModel: CreateViewModel = hiltViewModel(key = "DetailDesc")
    val uiState by createViewModel.uiState.collectAsState()

    fun clickLikeWithCheck() {
        if (createViewModel.isUserLoggedIn()) {
            onLikeClick()
        } else {
            createViewModel.updateLoginDialog(true)
            createViewModel.setLoginSource("default_avatar")
        }
    }

    Column(modifier = modifier) {
        Row(
            horizontalArrangement = Arrangement.spacedBy(4.dp),
            modifier = Modifier.padding(2.dp, 0.dp, 16.dp, 0.dp)
        ) {
            TextButton(onClick = { clickLikeWithCheck() }, shape = MaterialTheme.shapes.medium) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    Image(
                        painter = painterResource(
                            id = if (liked) R.drawable.icon_heart_red
                            else R.drawable.icon_heart_gray
                        ),
                        null,
                        modifier = Modifier.size(24.dp),
                    )
                    item.likedCount?.let {
                        if (it > 0) Text("$it", style = AppThemeTextStyle.Body11H)
                    }
                }
            }
            TextButton(onClick = { onCommentClick() }, shape = MaterialTheme.shapes.medium) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    Image(
                        painterResource(R.drawable.icon_comment),
                        null,
                        modifier = Modifier.size(20.dp)
                    )
                    item.commentCount?.let {
                        if (it > 0) Text("$it", style = AppThemeTextStyle.Body11H)
                    }
                }
            }
            TextButton(onClick = { onShareClick() }, shape = MaterialTheme.shapes.medium) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    Image(
                        painterResource(R.drawable.icon_share_line),
                        null,
                        modifier = Modifier.size(22.dp)
                    )
                    sharedCount.let {
                        if (it > 0) Text("$it", style = AppThemeTextStyle.Body11H)
                    }
                }
            }

            Spacer(modifier = Modifier.weight(1f))

            Button(
                onClick = { onSaveClick() }, colors = ButtonDefaults.buttonColors(
                    MaterialTheme.colorScheme.secondary, MaterialTheme.colorScheme.onSecondary
                ), shape = MaterialTheme.shapes.medium
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    Image(
                        painterResource(
                            if (saved) R.drawable.icon_saved_black
                            else R.drawable.icon_saved_line
                        ), null, modifier = Modifier.size(24.dp)
                    )
                    Text(stringResource(R.string.text_save))
                }
            }
        }

        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp, 12.dp),
            verticalArrangement = Arrangement.spacedBy(10.dp)
        ) {
            item.detailTitle?.let {
                Text(
                    text = it, style = AppThemeTextStyle.Body16H
                )
            }

            item.detailDescription?.let {
                Text(
                    text = it, style = AppThemeTextStyle.Body14H.copy(AppThemeColors.Gray700)
                )
            }

            item.detailTag?.let { tags ->
                FlowRow(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 8.dp),
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    tags.forEach { tag ->
                        Text(
                            text = tag,
                            style = AppThemeTextStyle.Body13H.copy(AppThemeColors.Blue500),
                            modifier = Modifier.clickable {
                                metric(
                                    SelectItem(
                                        itemListName = EventItemListName.Hashtag,
                                        method = EventMethod.Click,
                                        actionType = EventActionType.EnterHashtagDetail,
                                        items = listOf(
                                            EventItem(
                                                itemId = tag,
                                                itemName = tag,
                                                itemCategory = EventItemCategory.Hashtag,
                                            ),
                                            EventItem(
                                                itemId = if (item.type == "tryon") item.tryOnTaskId else item.moodboardId,
                                                itemName = item.detailTitle,
                                                itemCategory = if (item.type == "tryon") EventItemCategory.TryOnCollage else EventItemCategory.GeneralCollage,
                                            )
                                        )
                                    )
                                )
                                // 导航到hashtag详情页，去掉开头的#符号
                                val cleanedTag = tag.removePrefix("#")
                                navActions.navigateToHashtagDetail(cleanedTag)
                            }
                        )
                    }
                }
            }

            item.lastUpdated?.let { timestamp ->
                Text(
                    text = timeToNowString(timestamp),
                    style = AppThemeTextStyle.Body12LightH.copy(AppThemeColors.Gray600)
                )
            }
        }
    }

    if (uiState.showLoginDialog) {
        LoginModal(
            onDismiss = { createViewModel.updateLoginDialog(false) },
            isLoading = uiState.isLoading,
            onGoogleLogin = {
                createViewModel.loginWithGoogle(
                    context = context,
                    onSuccess = {
                        onLikeClick()
                    }
                )
                createViewModel.updateLoginDialog(false)
            },
            onAppleLogin = {
                createViewModel.loginWithApple(
                    context = context,
                    onSuccess = {
                        onLikeClick()
                    }
                )
                createViewModel.updateLoginDialog(false)
            }
        )
    }
}

fun timeToNowString(time: Long?): String {
    if (time == null) return ""

    val currentTime = System.currentTimeMillis() / 1000
    val diffInSeconds = currentTime - time.toLong()

    val str = when {
        diffInSeconds < 60 -> "just now"
        diffInSeconds < 3600 -> "${diffInSeconds / 60} minutes ago"
        diffInSeconds < 86400 -> "${diffInSeconds / 3600} hours ago"
        else -> "${diffInSeconds / 86400} days ago"
    }

    return str
}

@OptIn(ExperimentalTime::class)
fun isoToTimestampSeconds(iso: String): Long? {
    try {
        return isoWithoutOffsetToMillis(iso) / 1000
    } catch (e: Exception) {
        Timber.w(e)
        return null
    }
}

fun isoWithoutOffsetToMillis(
    isoLocal: String,
    offset: ZoneOffset = ZoneOffset.UTC,
): Long {
    val ldt = LocalDateTime.parse(isoLocal, DateTimeFormatter.ISO_LOCAL_DATE_TIME)
    return ldt.toInstant(offset).toEpochMilli()
}
