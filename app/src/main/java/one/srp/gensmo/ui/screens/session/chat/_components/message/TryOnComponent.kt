package one.srp.gensmo.ui.screens.session.chat._components.message

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.Text
import androidx.compose.material3.TextField
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.blur
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking
import one.srp.core.analytics.events.SelectItem
import one.srp.core.analytics.events.ViewItemList
import one.srp.core.analytics.types.EventActionType
import one.srp.core.analytics.types.EventItem
import one.srp.core.analytics.types.EventItemCategory
import one.srp.core.analytics.types.EventItemListName
import one.srp.core.analytics.types.EventMethod
import one.srp.core.analytics.types.EventRefer
import one.srp.gensmo.R
import one.srp.gensmo.data.model.ChatMessage
import one.srp.gensmo.data.model.ChatMessageRole
import one.srp.gensmo.data.model.CollectionType
import one.srp.gensmo.data.model.ProductItem
import one.srp.gensmo.data.model.SearchParams
import one.srp.gensmo.data.model.SearchQueryMessage
import one.srp.gensmo.data.model.SearchQueryMessageWrapper
import one.srp.gensmo.data.model.TryOnBackgroundParams
import one.srp.gensmo.data.model.TryOnChangebgLoadingMessage
import one.srp.gensmo.data.model.TryOnChangebgQueryMessage
import one.srp.gensmo.data.model.TryOnChangebgQueryMessageWrapper
import one.srp.gensmo.data.model.TryOnChangebgResMessage
import one.srp.gensmo.data.model.TryOnLoadingMessage
import one.srp.gensmo.data.model.TryOnParams
import one.srp.gensmo.data.model.TryOnQueryMessage
import one.srp.gensmo.data.model.TryOnQueryMessageWrapper
import one.srp.gensmo.data.model.TryOnResMessage
import one.srp.gensmo.data.model.TryOnResMessageWrapper
import one.srp.gensmo.data.model.TryOnTaskItem
import one.srp.gensmo.data.store.UserDataStoreManager
import one.srp.gensmo.ui.components.preview.ZoomDialog
import one.srp.gensmo.ui.screens.product.panel.info.ProductInfoDrawer
import one.srp.gensmo.ui.screens.session._components.MessageBox
import one.srp.gensmo.ui.screens.session._components.SecondaryIconTextButton
import one.srp.gensmo.ui.screens.session._components.TryOnSpanText
import one.srp.gensmo.ui.screens.session.chat._components.SaveActionContainer
import one.srp.gensmo.ui.screens.session.chat._components.SaveIconButton
import one.srp.gensmo.ui.screens.session.chat._components.SessionNavigation
import one.srp.gensmo.ui.screens.session.view._components.AlternativesView
import one.srp.gensmo.ui.screens.session.view._utils.productCanTryOn
import one.srp.gensmo.ui.theme.AppThemeColors
import one.srp.gensmo.ui.theme.AppThemeTextStyle
import one.srp.gensmo.utils.metrics.MetricViewModel
import java.util.UUID

@Composable
fun TryOnQueryComponent(item: TryOnQueryMessage) {
    Column(verticalArrangement = Arrangement.spacedBy(16.dp), horizontalAlignment = Alignment.End) {
        item.value.tryonQuery.internalImageList?.let {
            if (it.isNotEmpty()) {
                Row(horizontalArrangement = Arrangement.spacedBy(8.dp)) {
                    it.map { img ->
                        MessageBox(shape = MaterialTheme.shapes.small) {
                            ZoomDialog(modifier = Modifier.size(60.dp)) {
                                AsyncImage(img, null, modifier = Modifier.fillMaxSize())
                            }
                        }
                    }
                }
            }
        }

        MessageBox {
            Column(modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)) {
                Text(stringResource(R.string.text_try_on), style = AppThemeTextStyle.Body16H)
            }
        }
    }
}

@Composable
fun TryOnLoadingComponent(item: TryOnLoadingMessage? = null) {
    val progress = remember { mutableIntStateOf(1) }
    var modelUrl by remember { mutableStateOf<String?>(null) }

    LaunchedEffect(Unit) {
        var currentProgress = 1
        while (currentProgress < 99) {
            delay(125)
            currentProgress += 1
            progress.intValue = currentProgress
        }
    }

    LaunchedEffect(Unit) {
        val model = UserDataStoreManager.getModelInfo()
        modelUrl = if (model.first.isNullOrEmpty()) {
            "https://gem-user-image.favie.yesy.online/user_upload/20250301/3ff8ead9-86ea-4f6f-9a04-5b415d9f2943.png"
        } else {
            model.first
        }
    }

    Column(modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)) {
        Box(
            modifier = Modifier
                .fillMaxWidth(0.8f)
                .aspectRatio(3 / 4f)
                .padding(start = 16.dp)
                .background(AppThemeColors.Gray100.copy(alpha = 0.2f)),
            contentAlignment = Alignment.Center,
        ) {
            AsyncImage(
                model = ImageRequest.Builder(LocalContext.current).data(modelUrl).crossfade(true)
                    .build(),
                contentDescription = null,
                modifier = Modifier
                    .fillMaxSize()
                    .blur(radius = 20.dp)
                    .graphicsLayer {
                        alpha = 0.35f
                        scaleX = 1.0f
                        scaleY = 1.0f
                    },
                contentScale = ContentScale.Fit
            )

            Column(
                verticalArrangement = Arrangement.spacedBy(4.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                Text(
                    "Getting your Avatar dressed...", style = AppThemeTextStyle.Body16H
                )

                Text("${progress.intValue}%", style = AppThemeTextStyle.Body16H)
            }
        }
    }
}

@Composable
fun TryOnResComponent(
    item: TryOnResMessage,
    onMessage: (ChatMessage) -> Unit = {},
    onNavigate: (SessionNavigation, ChatMessage, Int?) -> Unit = { _, _, _ -> },
) {
    val result = item.value.tryonRes
    val productList = item.value.tryonRes.outfitComment?.searchStylist?.searchProductsResult

    val metricViewModel: MetricViewModel = hiltViewModel()
    val metric = metricViewModel.compatMetric(EventRefer.Channel)
    val metricItems = listOf(
        EventItem(
            itemCategory = EventItemCategory.Channel,
            itemId = item.sessionId,
        ), EventItem(
            itemCategory = EventItemCategory.TryOnTask,
            itemId = result.tryOnTaskId,
        )
    )

    fun onItemClick() {
        metric(
            SelectItem(
                itemListName = EventItemListName.TryOnResultList,
                method = EventMethod.Click,
                actionType = EventActionType.EnterTryOnDetail,
                items = metricItems,
            )
        )

        onNavigate(SessionNavigation.TryOnTask, item, null)
    }

    var alternativesOpen by remember { mutableStateOf(false) }
    fun onMoreLook() {
        alternativesOpen = true

        metric(
            SelectItem(
                itemListName = EventItemListName.MoreLooksBtn,
                method = EventMethod.Click,
                actionType = EventActionType.TryOnSelect,
                items = metricItems,
            )
        )
    }

    fun onRetry() {
        metric(
            SelectItem(
                itemListName = EventItemListName.RetryBtn,
                method = EventMethod.Click,
                actionType = EventActionType.TryOn,
                items = metricItems,
            )
        )

        item.value.tryonQuery?.let { tryonQuery ->
            onMessage(
                TryOnQueryMessage(
                    item.sessionId,
                    UUID.randomUUID().toString(),
                    ChatMessageRole.User.value,
                    false,
                    TryOnQueryMessageWrapper(
                        tryonQuery = tryonQuery.copy(messageId = item.messageId)
                    )
                )
            )
        }
    }

    var changeBgOpen by remember { mutableStateOf(false) }
    var changeBgInitialPrompt by remember {
        mutableStateOf(
            result.outfitComment?.scenarioGuru?.backgroundPrompt ?: ""
        )
    }

    fun onChangeBg(prompt: String = "") {
        metric(
            SelectItem(
                itemListName = EventItemListName.ScenarioBtn,
                method = EventMethod.Click,
                actionType = EventActionType.InitializeSceneEditBox,
                items = metricItems,
            )
        )

        changeBgInitialPrompt =
            prompt.ifBlank { result.outfitComment?.scenarioGuru?.backgroundPrompt ?: "" }
        changeBgOpen = true
    }

    fun changeBg(prompt: String) {
        val message = TryOnChangebgQueryMessage(
            sessionId = "default",
            messageId = UUID.randomUUID().toString(),
            role = ChatMessageRole.User.value,
            visible = false,
            value = TryOnChangebgQueryMessageWrapper(
                tryonChangebgQuery = TryOnBackgroundParams(
                    messageId = item.messageId,
                    imageUrl = result.tryOnUrl ?: "",
                    prompt = prompt,
                    negativePrompt = "",
                    tryOnTaskId = result.tryOnTaskId ?: "",
                    isAsync = true,
                ),
            )
        )

        onMessage(message)
    }

    fun tryOnProduct(product: ProductItem) {
        val message = TryOnQueryMessage(
            sessionId = "default",
            messageId = UUID.randomUUID().toString(),
            role = ChatMessageRole.User.value,
            visible = true,
            value = TryOnQueryMessageWrapper(
                tryonQuery = TryOnParams(
                    modelId = runBlocking { UserDataStoreManager.getModelInfo().second ?: "" },
                    products = listOf(product),
                    internalImageList = listOfNotNull(product.mainImage?.link),
                    isAsync = true,
                )
            )
        )
        onMessage(message)
    }

    fun remixProduct(product: ProductItem) {
        val message = SearchQueryMessage(
            sessionId = "default",
            messageId = UUID.randomUUID().toString(),
            role = ChatMessageRole.User.value,
            visible = true,
            value = SearchQueryMessageWrapper(
                searchQuery = SearchParams(
                    debugLevel = 0,
                    query = "Complete the look",
                    budget = "",
                    isAsync = true,
                    route = "",
                    isPresetQuery = false,
                    moodboardVersion = "v2",
                    imageUrl = product.mainImage?.link ?: "",
                    useOnlineImageSeg = false,
                    specifiedProduct = product,
                )
            )
        )
        onMessage(message)
    }

    if (result.status == "completed") {
        LaunchedEffect(Unit) {
            metric(
                ViewItemList(
                    itemListName = EventItemListName.TryOnResultList,
                    items = metricItems + if (result.changeBackgroundImagesUrl.isNullOrEmpty()) emptyList() else listOf(
                        EventItem(itemCategory = EventItemCategory.TryOnScenarioCollage)
                    ),
                )
            )
        }

        Column(verticalArrangement = Arrangement.spacedBy(12.dp)) {
            Column(modifier = Modifier.padding(horizontal = 16.dp)) {
                Text(
                    result.outfitComment?.outfitEnthusiast?.get(0)?.text
                        ?: "All set! Here's a look that'll make you feel amazing \uD83D\uDCAB",
                    style = AppThemeTextStyle.Body16H,
                )
            }

            TryOnView(
                modifier = Modifier.fillMaxWidth(),
                item = result,
                onItemClick = { onItemClick() },
                onItemSave = { state ->
                    metric(
                        SelectItem(
                            itemListName = EventItemListName.SaveBtn,
                            method = EventMethod.Click,
                            actionType = if (state) EventActionType.Save else EventActionType.Unsave,
                            items = metricItems + if (result.changeBackgroundImagesUrl.isNullOrEmpty()) emptyList() else listOf(
                                EventItem(itemCategory = EventItemCategory.TryOnScenarioCollage)
                            ),
                        )
                    )
                },
                moreLookEnable = item.value.tryonRes.moodboardId != null,
                onMoreLook = { onMoreLook() },
            )

            ActionView(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp),
                retryEnable = item.value.tryonQuery != null,
                onRetry = { onRetry() },
                changeBgEnable = true,
                onChangeBg = { onChangeBg() },
            )

            result.outfitComment?.searchStylist?.searchSuggestionComment?.let {
                Column(modifier = Modifier.padding(horizontal = 16.dp)) {
                    TryOnSpanText(text = it)
                }
            }

            productList?.let { productList ->
                ProductView(
                    modifier = Modifier.fillMaxWidth(),
                    items = productList,
                    onTryOn = { tryOnProduct(it) },
                    onRemix = { remixProduct(it) },
                    onMount = {
                        metric(
                            ViewItemList(
                                itemListName = EventItemListName.ProductList,
                                items = metricItems + productList.mapIndexed { index, it ->
                                    EventItem(
                                        itemCategory = EventItemCategory.Product,
                                        itemId = it.globalId,
                                        itemName = it.brand,
                                        index = index,
                                    )
                                },
                            )
                        )
                    },
                    onItemClick = {
                        metric(
                            SelectItem(
                                itemListName = EventItemListName.ProductListProduct,
                                method = EventMethod.Click,
                                actionType = EventActionType.EnterProductDetail,
                                items = metricItems + listOf(
                                    EventItem(
                                        itemCategory = EventItemCategory.Product,
                                        itemId = it.globalId,
                                        itemName = it.brand
                                    )
                                ),
                            )
                        )
                    })
            }

            ChangeBgView(
                open = changeBgOpen,
                initialPrompt = changeBgInitialPrompt,
                onClose = { changeBgOpen = false },
                onCommit = { changeBg(it) },
            )

            AlternativesView(
                open = alternativesOpen,
                onClose = { alternativesOpen = false },
                moodboardId = result.moodboardId,
                onCommit = {
                    onMessage(
                        TryOnQueryMessage(
                            sessionId = item.sessionId,
                            messageId = UUID.randomUUID().toString(),
                            role = ChatMessageRole.User.value,
                            visible = true,
                            value = TryOnQueryMessageWrapper(tryonQuery = it),
                        )
                    )
                })
        }
    } else {
        Column(verticalArrangement = Arrangement.spacedBy(12.dp)) {
            Row(modifier = Modifier.padding(horizontal = 16.dp)) {
                Text(
                    stringResource(R.string.text_well_that_was_unexpected_one_more_time),
                    style = AppThemeTextStyle.Body16H
                )
            }

            ActionView(
                modifier = Modifier.padding(horizontal = 16.dp),
                retryEnable = true,
                onRetry = { onRetry() },
            )
        }
    }
}

@Composable
private fun TryOnView(
    modifier: Modifier = Modifier,
    item: TryOnTaskItem,
    onItemClick: () -> Unit = {},
    onItemSave: (Boolean) -> Unit = {},
    moreLookEnable: Boolean = false,
    onMoreLook: () -> Unit = {},
) {
    val changeBgList = item.changeBackgroundImagesUrl ?: emptyList()

    BoxWithConstraints(modifier = modifier) {
        val maxWidth = this.maxWidth
        val itemWidth = maxWidth * 0.75f

        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(12.dp),
            contentPadding = PaddingValues(horizontal = 16.dp)
        ) {
            item {
                Box {
                    AsyncImage(
                        model = ImageRequest.Builder(LocalContext.current).data(item.tryOnUrl)
                            .crossfade(true).build(),
                        contentDescription = null,
                        contentScale = ContentScale.FillWidth,
                        modifier = Modifier
                            .width(itemWidth)
                            .height(itemWidth / 0.75f)
                            .clickable { onItemClick() },
                        alignment = Alignment.CenterStart
                    )

                    item.tryOnTaskId?.let {
                        SaveActionContainer(
                            type = CollectionType.TryOn,
                            id = item.tryOnTaskId,
                            initialState = item.isFavorited == true,
                            onAction = {
                                item.isFavorited = it
                                onItemSave(it)
                            }) { state, onClick ->
                            SaveIconButton(
                                modifier = Modifier.align(Alignment.TopEnd),
                                enable = state,
                                onClick = onClick
                            )
                        }
                    }

                    if (moreLookEnable) {
                        Button(
                            onClick = { onMoreLook() },
                            modifier = Modifier
                                .height(48.dp)
                                .align(Alignment.BottomCenter)
                                .padding(bottom = 16.dp),
                            shape = MaterialTheme.shapes.large,
                            colors = ButtonDefaults.outlinedButtonColors(
                                containerColor = AppThemeColors.White,
                                contentColor = AppThemeColors.Black
                            ),
                            contentPadding = PaddingValues(vertical = 2.dp, horizontal = 12.dp),
                            elevation = ButtonDefaults.buttonElevation(1.dp),
                        ) {
                            Row(
                                horizontalArrangement = Arrangement.spacedBy(4.dp),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Image(
                                    painterResource(R.drawable.icon_tryon_product),
                                    null,
                                    modifier = Modifier.size(18.dp)
                                )

                                Text(
                                    text = stringResource(R.string.text_try_more),
                                    style = AppThemeTextStyle.Body16H,
                                    color = Color.Black
                                )
                            }
                        }
                    }
                }
            }

            items(changeBgList.size) { index ->
                ZoomDialog(
                    modifier = Modifier
                        .width(itemWidth)
                        .height(itemWidth / 0.75f)
                ) {
                    AsyncImage(
                        model = ImageRequest.Builder(LocalContext.current)
                            .data(changeBgList[index].imageUrl).crossfade(true).build(),
                        contentDescription = null,
                        contentScale = ContentScale.Fit,
                        modifier = Modifier.fillMaxSize(),
                        alignment = Alignment.Center
                    )
                }
            }
        }
    }
}

@Composable
private fun ActionView(
    modifier: Modifier = Modifier,
    retryEnable: Boolean = false,
    onRetry: () -> Unit = {},
    changeBgEnable: Boolean = false,
    onChangeBg: () -> Unit = {},
) {
    LazyRow(
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        modifier = modifier,
    ) {


        if (retryEnable) {
            item {
                SecondaryIconTextButton(
                    onClick = { onRetry() },
                    icon = {
                        Image(
                            painterResource(R.drawable.icon_refresh),
                            null,
                            modifier = Modifier.size(18.dp)
                        )
                    },
                    text = stringResource(R.string.text_retry),
                )
            }
        }

        if (changeBgEnable) {
            item {
                SecondaryIconTextButton(
                    onClick = { onChangeBg() },
                    icon = {
                        Image(
                            painterResource(R.drawable.icon_tryon_background),
                            null,
                            modifier = Modifier.size(18.dp)
                        )
                    },
                    text = stringResource(R.string.text_vibe_imagine),
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun ChangeBgView(
    open: Boolean = false,
    initialPrompt: String = "",
    onClose: () -> Unit = {},
    onCommit: (String) -> Unit = {},
) {
    val sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)
    val focusRequester = remember { FocusRequester() }

    var input by remember { mutableStateOf(initialPrompt) }

    fun commit() {
        onCommit(input)
        onClose()
    }

    if (open) {
        ModalBottomSheet(
            onDismissRequest = { onClose() },
            sheetState = sheetState,
            dragHandle = null,
        ) {
            LaunchedEffect(Unit) {
                delay(300)
                focusRequester.requestFocus()
            }

            Column(verticalArrangement = Arrangement.spacedBy(4.dp)) {
                Row(verticalAlignment = Alignment.CenterVertically) {
                    IconButton(onClick = { onClose() }) { Icon(Icons.Default.Close, null) }

                    Column(
                        verticalArrangement = Arrangement.Center,
                        horizontalAlignment = Alignment.CenterHorizontally,
                        modifier = Modifier.weight(1f)
                    ) {
                        Text(
                            stringResource(R.string.text_describe_your_moment),
                            style = AppThemeTextStyle.Heading16H
                        )
                    }

                    Spacer(modifier = Modifier.size(40.dp))
                }

                Box(
                    modifier = Modifier
                        .padding(horizontal = 16.dp)
                        .padding(bottom = 12.dp)
                ) {
                    Column(
                        modifier = Modifier.border(1.dp, AppThemeColors.Gray100),
                    ) {
                        TextField(
                            value = input,
                            onValueChange = { input = it },
                            placeholder = {
                                Text(
                                    stringResource(R.string.text_edit_to_continue),
                                    style = AppThemeTextStyle.Body16H
                                )
                            },
                            modifier = Modifier
                                .focusRequester(focusRequester)
                                .fillMaxWidth()
                                .padding(bottom = 4.dp),
                            singleLine = false,
                            maxLines = 6,
                            minLines = 4,
                            textStyle = AppThemeTextStyle.Body16H,
                            colors = TextFieldDefaults.colors(
                                unfocusedContainerColor = Color.Transparent,
                                focusedContainerColor = Color.Transparent,
                                unfocusedIndicatorColor = Color.Transparent,
                                focusedIndicatorColor = Color.Transparent
                            )
                        )

                        Row(modifier = Modifier.padding(8.dp)) {
                            Spacer(modifier = Modifier.weight(1f))

                            Button(
                                onClick = { commit() },
                                shape = MaterialTheme.shapes.medium,
                                contentPadding = PaddingValues(0.dp),
                                modifier = Modifier.size(40.dp),
                                enabled = input.isNotBlank(),
                            ) {
                                Image(painterResource(R.drawable.icon_arrow_up), null)
                            }
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun ProductView(
    modifier: Modifier = Modifier,
    items: List<ProductItem>,
    onTryOn: (ProductItem) -> Unit = {},
    onRemix: (ProductItem) -> Unit = {},
    onMount: () -> Unit = {},
    onItemClick: (ProductItem) -> Unit = {},
) {
    var selected by remember { mutableStateOf<ProductItem?>(null) }

    LaunchedEffect(Unit) {
        onMount()
    }

    fun openProduct(product: ProductItem) {
        selected = product
        onItemClick(product)
    }

    fun onClose() {
        selected = null
    }

    Column(
        modifier = Modifier
            .background(MaterialTheme.colorScheme.surface)
            .then(modifier)
    ) {
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(6.dp),
            contentPadding = PaddingValues(6.dp),
        ) {
            items(items.size) { index ->
                items[index].let { product ->
                    Box(
                        modifier = Modifier
                            .padding(vertical = 2.dp)
                            .clickable { openProduct(product) }) {
                        Box(
                            modifier = Modifier
                                .clip(MaterialTheme.shapes.small)
                                .size(90.dp)
                                .background(AppThemeColors.White),
                            contentAlignment = Alignment.Center,
                        ) {
                            AsyncImage(
                                product.mainImage?.link,
                                null,
                                modifier = Modifier.height(90.dp),
                                alignment = Alignment.Center,
                            )
                        }
                    }
                }
            }
        }
    }

    ProductInfoDrawer(
        open = selected != null,
        item = selected,
        onClose = { onClose() },
        onTryOn = {
            selected?.let {
                onTryOn(it)
                onClose()
            }
        },
        onRemix = {
            selected?.let {
                onRemix(it)
                onClose()
            }
        },
        tryOnEnabled = productCanTryOn(selected),
    )
}

@Composable
fun TryOnChangebgLoadingComponent(
    item: TryOnChangebgLoadingMessage,
    onMessage: (ChatMessage) -> Unit = {},
    onNavigate: (SessionNavigation, ChatMessage, Int?) -> Unit = { _, _, _ -> },
) {
    // TODO: dev
    item.value.tryonRes?.let {
        val processed = TryOnResMessage(
            sessionId = item.sessionId,
            messageId = item.messageId,
            role = item.role,
            visible = item.visible,
            value = TryOnResMessageWrapper(
                tryonQuery = null, tryonRes = item.value.tryonRes
            )
        )

        return TryOnResComponent(processed, onMessage, onNavigate)
    } ?: run {
        return TryOnLoadingComponent()
    }
}

@Composable
fun TryOnChangebgResComponent(
    item: TryOnChangebgResMessage,
    onMessage: (ChatMessage) -> Unit = {},
    onNavigate: (SessionNavigation, ChatMessage, Int?) -> Unit = { _, _, _ -> },
) {
    val processed = TryOnResMessage(
        sessionId = item.sessionId,
        messageId = item.messageId,
        role = item.role,
        visible = item.visible,
        value = TryOnResMessageWrapper(
            tryonQuery = null, tryonRes = item.value.tryonRes
        )
    )

    return TryOnResComponent(processed, onMessage, onNavigate)
}
