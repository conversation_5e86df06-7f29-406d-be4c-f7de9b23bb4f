package one.srp.gensmo.ui.screens.user.profile._viewmodel

import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import one.srp.gensmo.data.remote.AccountService
import one.srp.gensmo.data.remote.UserService
import one.srp.gensmo.data.store.UserDataStoreManager
import one.srp.gensmo.utils.auth.AuthManager
import timber.log.Timber
import javax.inject.Inject
import one.srp.gensmo.data.model.PostUserProfile

@HiltViewModel
class UserProfileViewModel @Inject constructor(
    private val authManager: AuthManager,
) : ViewModel(), DefaultLifecycleObserver {
    private val _isLoggedIn = MutableStateFlow(false)
    val isLoggedIn: StateFlow<Boolean> = _isLoggedIn.asStateFlow()

    private val _userName = MutableStateFlow("Guest")
    val userName: StateFlow<String> = _userName.asStateFlow()

    private val _userAvatar = MutableStateFlow<String?>("icon_random_thumb")
    val userAvatar: StateFlow<String?> = _userAvatar.asStateFlow()

    // 用户统计数据
    private val _followingCount = MutableStateFlow(0)
    val followingCount: StateFlow<Int> = _followingCount.asStateFlow()

    private val _followerCount = MutableStateFlow(0)
    val followerCount: StateFlow<Int> = _followerCount.asStateFlow()

    private val _totalLikes = MutableStateFlow(0)
    val totalLikes: StateFlow<Int> = _totalLikes.asStateFlow()

    init {
        checkLoginStatus()
        getUserInfo()
    }

    fun logout() {
        viewModelScope.launch {
            try {
                authManager.signOut()
                _isLoggedIn.value = false  // 更新登录状态
                _userName.value = "Guest"   // 重置用户名
                _userAvatar.value = "icon_unregister"  // 设置为未注册图标
                UserDataStoreManager.initializeGuestTokenIfNeeded()
            } catch (e: Exception) {
                Timber.e(e, "登出失败")
            }
        }
    }

    fun getUserInfo() {
        viewModelScope.launch {
            val userInfo = UserDataStoreManager.getUserInfo()
            if (userInfo != null) {
                _userName.value = userInfo.name ?: "Hello"
                _userAvatar.value = userInfo.avatar ?: "icon_random_thumb"
                
                // 获取用户统计数据
                try {
                    val userId = UserDataStoreManager.getUserId()
                    if (!userId.isNullOrEmpty()) {
                        val profileResponse = UserService.api.getUserProfile(userId)
                        if (profileResponse.isSuccessful) {
                            val profile = profileResponse.body()
                            profile?.let {
                                _followingCount.value = it.followingCount ?: 0
                                _followerCount.value = it.followerCount ?: 0
                                _totalLikes.value = it.totalLikes ?: 0
                            }
                        }
                    }
                } catch (e: Exception) {
                    Timber.e(e, "获取用户统计数据失败")
                }
            } else {
                _userName.value = "Guest"
                _userAvatar.value = "icon_unregister"
                // 重置统计数据
                _followingCount.value = 0
                _followerCount.value = 0
                _totalLikes.value = 0
            }
        }
    }

    fun updateUserAvatar(imageUrl: String?) {
        viewModelScope.launch {
            val curr = UserDataStoreManager.getUserInfo()
            if (curr == null) return@launch

            UserDataStoreManager.saveUserInfo(curr.copy(avatar = imageUrl))

            getUserInfo()
        }
    }

    fun checkLoginStatus() {
        viewModelScope.launch {
            try {
                val isUserLoggedIn = UserDataStoreManager.isUserLoggedIn()
                _isLoggedIn.value = isUserLoggedIn
                Timber.d("用户登录状态: $isUserLoggedIn")
            } catch (e: Exception) {
                Timber.e(e, "检查登录状态失败")
                _isLoggedIn.value = false
            }
        }
    }

    fun deleteAccount() {
        viewModelScope.launch {
            try {
                val response = AccountService.api.deleteUser()
                if (response.isSuccessful) {
                    Timber.d("账户删除成功")
                    logout()
                } else {
                    Timber.e("账户删除失败: ${response.errorBody()?.string()}")
                }
            } catch (e: Exception) {
                Timber.e(e, "删除账户失败")
            }
        }
    }

    suspend fun getCurrentUserId(): String? {
        return UserDataStoreManager.getUserId()
    }

    fun updateUserName(newUserName: String) {
        viewModelScope.launch {
            try {
                // 调用API更新用户名
                val response = UserService.api.postUserProfile(
                    PostUserProfile(userName = newUserName)
                )
                
                if (response.isSuccessful) {
                    // 更新本地状态
                    _userName.value = newUserName
                    
                    // 更新本地存储的用户信息
                    val curr = UserDataStoreManager.getUserInfo()
                    if (curr != null) {
                        UserDataStoreManager.saveUserInfo(curr.copy(name = newUserName))
                    }
                    
                    Timber.d("用户名更新成功: $newUserName")
                } else {
                    Timber.e("用户名更新失败: ${response.errorBody()?.string()}")
                }
            } catch (e: Exception) {
                Timber.e(e, "更新用户名时发生异常")
            }
        }
    }

}