package one.srp.gensmo.ui.screens.notification

import android.net.Uri
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.*
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.pulltorefresh.PullToRefreshBox
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalUriHandler
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import one.srp.gensmo.R
import one.srp.gensmo.data.remote.apis.NotificationItem
import one.srp.gensmo.ui.components.exception.BaseError
import one.srp.gensmo.ui.components.navigate.TopBar
import one.srp.gensmo.ui.navigation.NavActions
import one.srp.gensmo.ui.theme.AppThemeTextStyle
import one.srp.gensmo.viewmodel.notification.NotificationViewModel
import one.srp.gensmo.utils.metrics.MetricViewModel
import one.srp.core.analytics.events.SelectItem
import one.srp.core.analytics.types.EventItem
import one.srp.core.analytics.types.EventItemCategory
import one.srp.core.analytics.types.EventItemListName
import one.srp.core.analytics.types.EventMethod
import one.srp.core.analytics.types.EventRefer
import timber.log.Timber
import java.text.SimpleDateFormat
import java.util.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun NotificationScreen(
    navActions: NavActions = NavActions(),
    viewModel: NotificationViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    val uriHandler = LocalUriHandler.current
    
    // 埋点：通知页面全局曝光
    val metricViewModel: MetricViewModel = hiltViewModel()
    val metric = metricViewModel.compatMetric(EventRefer.Notification)
    
    LaunchedEffect(Unit) {
        metric(
            SelectItem(
                itemListName = EventItemListName.Screen,
                method = EventMethod.PageView
            )
        )
    }
    
    // 处理下拉刷新状态

    Scaffold(
        topBar = {
            TopBar(
                immersive = true,
                onBack = { navActions.back() },
                modifier = Modifier.background(Color.White)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "Notifications",
                        style = AppThemeTextStyle.Heading16D,
                        color = MaterialTheme.colorScheme.onBackground
                    )
                    
                    // 全部已读按钮
                    val hasUnreadNotifications = uiState.notifications.any { it.status == "unread" }
                    if (hasUnreadNotifications) {
                        TextButton(
                            onClick = { viewModel.markAllNotificationsAsRead() },
                            colors = ButtonDefaults.textButtonColors(
                                contentColor = MaterialTheme.colorScheme.primary
                            ),
                            modifier = Modifier.padding(horizontal = 4.dp)
                        ) {
                            Text(
                                text = "Mark all read",
                                style = AppThemeTextStyle.Body12H,
                                color = MaterialTheme.colorScheme.primary.copy(alpha = 0.8f)
                            )
                        }
                    }
                }
            }
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            PullToRefreshBox(
                modifier = Modifier.fillMaxSize(),
                isRefreshing = uiState.isLoading,
                onRefresh = { viewModel.refresh() }
            ) {
                when {
                    uiState.error != null && uiState.notifications.isEmpty() -> {
                        BaseError(
                            onClick = { 
                                viewModel.clearError()
                                viewModel.refresh()
                            }
                        )
                    }
                    
                    uiState.notifications.isEmpty() && !uiState.isLoading -> {
                        NotificationEmptyState()
                    }
                    
                    else -> {
                        NotificationList(
                            notifications = uiState.notifications,
                            isLoadingMore = uiState.isLoadingMore,
                            hasMore = uiState.hasMore,
                            onLoadMore = { viewModel.loadMore() },
                            onItemClick = { item ->
                                // 埋点：通知条目点击
                                metric(
                                    SelectItem(
                                        itemListName = EventItemListName.NotificationListItem,
                                        method = EventMethod.Click,
                                        items = listOf(
                                            EventItem(
                                                itemCategory = EventItemCategory.Notification,
                                                itemId = item.notificationId
                                            )
                                        )
                                    )
                                )
                                
                                // 标记通知为已读
                                viewModel.markNotificationsAsRead(listOf(item.notificationId))
                                // 处理通知点击跳转
                                handleNotificationClick(item, uriHandler, navActions)
                            }
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun NotificationList(
    notifications: List<NotificationItem>,
    isLoadingMore: Boolean,
    hasMore: Boolean,
    onLoadMore: () -> Unit,
    onItemClick: (NotificationItem) -> Unit
) {
    LazyColumn(
        modifier = Modifier.fillMaxSize(),
        contentPadding = PaddingValues(vertical = 8.dp)
    ) {
        items(notifications) { notification ->
            NotificationItem(
                notification = notification,
                onClick = { onItemClick(notification) }
            )
        }
        
        // 加载更多指示器
        if (hasMore && notifications.isNotEmpty()) {
            item {
                LaunchedEffect(Unit) {
                    onLoadMore()
                }
                
                if (isLoadingMore) {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(24.dp),
                            color = MaterialTheme.colorScheme.primary
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun NotificationItem(
    notification: NotificationItem,
    onClick: () -> Unit
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 4.dp)
            .clickable { onClick() }
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.spacedBy(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 头像容器，用于定位红点
            Box {
                // 发送者头像
                AsyncImage(
                    model = ImageRequest.Builder(LocalContext.current)
                        .data(notification.senderAvatar)
                        .crossfade(true)
                        .build(),
                    contentDescription = "头像",
                    modifier = Modifier
                        .size(48.dp)
                        .clip(CircleShape),
                    contentScale = ContentScale.Crop
                )
                
                // 未读标识 - 更靠近图标左上角
                if (notification.status == "unread") {
                    Box(
                        modifier = Modifier
                            .size(8.dp)
                            .clip(CircleShape)
                            .background(MaterialTheme.colorScheme.error)
                            .align(Alignment.TopStart)
                            .offset(x = (-2).dp, y = (-2).dp)
                    )
                }
            }
            
            // 通知内容
            Column(
                modifier = Modifier
                    .weight(1f),
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                // 通知内容
                Text(
                    text = notification.content,
                    style = AppThemeTextStyle.Body14H,
                    color = MaterialTheme.colorScheme.onSurface,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis
                )
                
                // 时间
                Text(
                    text = formatRelativeTime(notification.createdAt),
                    style = AppThemeTextStyle.Body12H,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                )
            }
        }
    }
}

/**
 * 处理通知点击事件，跳转到对应链接
 */
private fun handleNotificationClick(
    notification: NotificationItem,
    uriHandler: androidx.compose.ui.platform.UriHandler,
    navActions: NavActions
) {
    try {
        val link = notification.link
        val uri = Uri.parse(link)
        
        Timber.d("通知点击，链接: $link")
        
        // 检查是否是内部深度链接
        if (uri.scheme == "gensmo") {
            // 使用项目的深度链接处理逻辑
            when ("${uri.host}${uri.path}") {
                "feed/comboDetail" -> {
                    val feedId = uri.getQueryParameter("feed_id")
                    val feedType = uri.getQueryParameter("feed_type")
                    
                    feedId?.let {
                        if (feedType == "try_on") {
                            navActions.navigateToTryOnDetail(it)
                        } else {
                            navActions.navigateToFeedDetail(it)
                        }
                    }
                }
                
                "tryon2" -> {
                    val taskId = uri.getQueryParameter("fetchTryonTaskId")
                    taskId?.let {
                        navActions.navigateToTryOnTask(it)
                    }
                }
                
                "search" -> {
                    val taskId = uri.getQueryParameter("task_id")
                    taskId?.let {
                        navActions.navigateToCollageTask(it)
                    }
                }
                
                "topic/detail" -> {
                    val topic = uri.getQueryParameter("topic")
                    topic?.let {
                        val cleanTopic = it.removePrefix("#")
                        navActions.navigateToHashtagDetail(cleanTopic)
                    }
                }
                
                "user/profile" -> {
                    val userId = uri.getQueryParameter("userId")
                    userId?.let {
                        navActions.navigateToUserPublicProfile(it)
                    }
                }
                
                "webview" -> {
                    val url = uri.getQueryParameter("url")
                    url?.let { encodedUrl ->
                        try {
                            val decodedUrl = Uri.decode(encodedUrl)
                            navActions.navigateToActivity(decodedUrl)
                        } catch (e: Exception) {
                            Timber.e(e, "解码URL失败")
                        }
                    }
                }
                
                else -> {
                    // 其他内部链接，使用通用的Activity页面打开
                    navActions.navigateToActivity(link)
                }
            }
        } else if (uri.scheme == "https" || uri.scheme == "http") {
            // 外部链接，使用Activity页面打开
            navActions.navigateToActivity(link)
        } else {
            // 其他类型的链接，尝试用系统浏览器打开
            uriHandler.openUri(link)
        }
        
    } catch (e: Exception) {
        Timber.e(e, "处理通知链接失败: ${notification.link}")
        // 错误处理，可以显示Toast或其他提示
    }
}

/**
 * 格式化相对时间显示
 */
private fun formatRelativeTime(timeString: String): String {
    return try {
        val inputFormat = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSSSS", Locale.getDefault())
        val date = inputFormat.parse(timeString)
        date?.let {
            val diffInSeconds = (System.currentTimeMillis() - it.time) / 1000L
            
            when {
                diffInSeconds < 60L -> "just now"
                diffInSeconds < 3600L -> {
                    val minutes = diffInSeconds / 60L
                    if (minutes == 1L) "1m ago" else "${minutes}m ago"
                }
                diffInSeconds < 86400L -> {
                    val hours = diffInSeconds / 3600L
                    if (hours == 1L) "1h ago" else "${hours}h ago"
                }
                diffInSeconds < 2592000L -> { // 30 days
                    val days = diffInSeconds / 86400L
                    if (days == 1L) "1 day ago" else "$days days ago"
                }
                else -> {
                    // 超过30天显示具体日期
                    SimpleDateFormat("MMM dd", Locale.ENGLISH).format(it)
                }
            }
        } ?: timeString
    } catch (e: Exception) {
        // 如果解析失败，返回原始字符串
        timeString
    }
}

@Composable
private fun NotificationEmptyState() {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(32.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        // 空状态图标
        Icon(
            painter = painterResource(id = R.drawable.icon_notification_empty),
            contentDescription = "空通知",
            modifier = Modifier.size(120.dp),
            tint = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
        )
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // 主标题
        Text(
            text = stringResource(R.string.text_no_notifications_yet),
            style = AppThemeTextStyle.Heading16D,
            color = MaterialTheme.colorScheme.onSurface,
            textAlign = TextAlign.Center
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // 副标题
        Text(
            text = stringResource(R.string.text_share_your_vibe_to_get_feedback),
            style = AppThemeTextStyle.Body14H,
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f),
            textAlign = TextAlign.Center
        )
    }
}
