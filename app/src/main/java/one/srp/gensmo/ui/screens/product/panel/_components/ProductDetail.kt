package one.srp.gensmo.ui.screens.product.panel._components

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowOutward
import androidx.compose.material.icons.filled.Autorenew
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.IconButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import one.srp.core.analytics.events.SelectItem
import one.srp.core.analytics.types.EventActionType
import one.srp.core.analytics.types.EventItem
import one.srp.core.analytics.types.EventItemCategory
import one.srp.core.analytics.types.EventItemListName
import one.srp.core.analytics.types.EventMethod
import one.srp.core.analytics.types.EventRefer
import one.srp.gensmo.R
import one.srp.gensmo.data.model.ProductItem
import one.srp.gensmo.ui.components.preview.ZoomDialog
import one.srp.gensmo.ui.theme.AppThemeElevation
import one.srp.gensmo.ui.theme.AppThemeTextStyle
import one.srp.gensmo.utils.metrics.MetricViewModel

@Composable
fun ProductDetail(
    item: ProductItem,
    saved: Boolean = false,
    replaceable: Boolean = false,
    onSave: () -> Unit = {},
    onOutJump: () -> Unit = {},
    onReplace: () -> Unit = {},
    onTryOn: () -> Unit = {},
    tryOnEnabled: Boolean = true,
    refer: EventRefer? = null,
) {
    // Metric helper for product detail refer
    val metricViewModel: MetricViewModel = hiltViewModel()
    val metric = metricViewModel.compatMetric(refer ?: EventRefer.ProductDetail)

    Column(verticalArrangement = Arrangement.spacedBy(16.dp)) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .aspectRatio(1f)
                .background(MaterialTheme.colorScheme.surface)
        ) {
            // Clickable modifier for product main image
            val zoomModifier = Modifier.clickable {
                metric(
                    SelectItem(
                        itemListName = EventItemListName.ProductMainPic,
                        method = EventMethod.Click
                    )
                )
            }
            ZoomDialog(modifier = zoomModifier) {
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(8.dp),
                    verticalArrangement = Arrangement.Center,
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    AsyncImage(
                        ImageRequest.Builder(LocalContext.current).data(item.mainImage?.link)
                            .crossfade(true).build(), null
                    )
                }
            }

            Box(
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .padding(12.dp)
            ) {
                IconButton(
                    colors = IconButtonDefaults.iconButtonColors(
                        MaterialTheme.colorScheme.background
                    ), onClick = {
                        // 埋点：商品详情页保存/取消保存按钮点击
                        metric(
                            SelectItem(
                                itemListName = EventItemListName.SaveBtn,
                                method = EventMethod.Click,
                                actionType = if (saved) EventActionType.Unsave else EventActionType.Save,
                                items = listOf(
                                    EventItem(
                                        itemCategory = EventItemCategory.Product,
                                        itemId = item.globalId,
                                        itemName = item.title
                                    )
                                )
                            )
                        )
                        onSave()
                    }) {
                    Image(
                        painterResource(
                            if (saved) R.drawable.icon_saved_black else R.drawable.icon_saved_line
                        ), null
                    )
                }
            }

            if (tryOnEnabled) {
                Box(
                    modifier = Modifier
                        .align(Alignment.BottomCenter)
                        .offset(y = (-8).dp)
                        .width(83.dp)
                        .height(34.dp)
                        .border(
                            width = 1.dp,
                            brush = Brush.linearGradient(
                                colors = listOf(
                                    Color(0xFFC1C1C1), // 左下角灰色
                                    Color(0xFFFFFFFF), // 中间白色过渡
                                    Color(0xFFC1C1C1)  // 右上角灰色
                                ),
                                start = Offset(0f, Float.POSITIVE_INFINITY), // 左下角
                                end = Offset(Float.POSITIVE_INFINITY, 0f) // 右上角
                            ),
                            shape = RoundedCornerShape(size = 12.dp)
                        )
                        .background(
                            color = Color(0xFFFFFFFF),
                            shape = RoundedCornerShape(size = 12.dp)
                        )
                        .clickable { onTryOn() },
                    contentAlignment = Alignment.Center
                ) {
                    Row(
                        horizontalArrangement = Arrangement.spacedBy(4.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            painter = painterResource(R.drawable.icon_tryon_product),
                            contentDescription = null,
                            modifier = Modifier.size(16.dp),
                            tint = Color.Black
                        )
                        Text(
                            text = stringResource(R.string.text_try_on),
                            style = AppThemeTextStyle.Body16H,
                            color = Color.Black
                        )
                    }
                }
            }

            if (replaceable) {
                Button(
                    onClick = { onReplace() },
                    modifier = Modifier
                        .align(Alignment.BottomCenter)
                        .height(32.dp)
                        .offset(y = 16.dp),
                    shape = MaterialTheme.shapes.small,
                    colors = ButtonDefaults.buttonColors(
                        MaterialTheme.colorScheme.surface, MaterialTheme.colorScheme.onSurface
                    ),
                    elevation = ButtonDefaults.elevatedButtonElevation(AppThemeElevation.small)
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Icon(Icons.Default.Autorenew, null)
                        Text(stringResource(R.string.text_swap_into_collage))
                    }
                }
            }
        }

        Column(
            verticalArrangement = Arrangement.spacedBy(12.dp),
            modifier = Modifier.padding(20.dp, 8.dp)
        ) {
            Text(
                text = when {
                    item.brand == null || item.brand.isEmpty() -> "Curated pick"
                    item.brandParentName != null && item.brandParentName.isNotEmpty() -> "${item.brand} by ${item.brandParentName}"
                    else -> item.brand
                },
                style = AppThemeTextStyle.Heading16H
            )

            item.title?.let {
                Text(it, style = AppThemeTextStyle.Body14LightH)
            }

            Row(verticalAlignment = Alignment.CenterVertically) {
                item.buyboxWinner?.price?.raw?.let {
                    Text(it, style = AppThemeTextStyle.Heading16H)
                }

                Spacer(modifier = Modifier.weight(1f))

                Button(
                    onClick = { onOutJump() },
                    modifier = Modifier.height(24.dp),
                    colors = ButtonDefaults.buttonColors(
                        MaterialTheme.colorScheme.surface, MaterialTheme.colorScheme.onSurface
                    ),
                    shape = MaterialTheme.shapes.medium,
                    contentPadding = PaddingValues(0.dp),
                ) {
                    Row(
                        horizontalArrangement = Arrangement.spacedBy(4.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        AsyncImage(item.sourceIcon, null, modifier = Modifier.size(16.dp))
                        Text(item.source ?: "", style = AppThemeTextStyle.Body11LightH)
                        Icon(Icons.Default.ArrowOutward, null, modifier = Modifier.size(16.dp))
                    }
                }
            }
        }
    }
}
