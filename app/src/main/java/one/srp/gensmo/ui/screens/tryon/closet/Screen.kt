package one.srp.gensmo.ui.screens.tryon.closet

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.CenterAlignedTopAppBar
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import kotlinx.coroutines.runBlocking
import one.srp.core.analytics.events.SelectItem
import one.srp.core.analytics.types.EventItem
import one.srp.core.analytics.types.EventItemCategory
import one.srp.core.analytics.types.EventItemListName
import one.srp.core.analytics.types.EventMethod
import one.srp.core.analytics.types.EventRefer
import one.srp.gensmo.data.store.UserDataStoreManager
import one.srp.gensmo.ui.navigation.NavActions
import one.srp.gensmo.ui.screens.tryon.closet._components.AvatarCard
import one.srp.gensmo.ui.screens.tryon.closet._components.AvatarHistory
import one.srp.gensmo.ui.screens.tryon.closet._components.EmptyAvatarCard
import one.srp.gensmo.ui.screens.tryon.closet._components.ErrorState
import one.srp.gensmo.ui.screens.tryon.closet._components.LoadingState
import one.srp.gensmo.ui.screens.tryon.closet._components.Progress
import one.srp.gensmo.ui.screens.tryon.closet._components.TaskLoadingCard
import one.srp.gensmo.ui.theme.AppThemeTextStyle
import one.srp.gensmo.utils.metrics.MetricViewModel
import one.srp.gensmo.viewmodel.tryon.ClosetViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ClosetScreen(
    viewModel: ClosetViewModel = hiltViewModel(),
    navActions: NavActions,
) {
    val metricViewModel: MetricViewModel = hiltViewModel()
    val metric = metricViewModel.compatMetric(EventRefer.AvatarManage)
    val uiState by viewModel.uiState.collectAsState()
    val replicaHistory by viewModel.replicaHistory.collectAsState()
    val hasMoreReplicaHistory by viewModel.hasMoreReplicaHistory.collectAsState()
    val currentModelId by viewModel.currentModelId.collectAsState()

    val isBackButtonEnabled = remember { mutableStateOf(true) }

    LaunchedEffect(Unit) {
        viewModel.getReplica()
        viewModel.getReplicaHistory()
    }

    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        CenterAlignedTopAppBar(
            title = {
                Text(
                    text = "Manage Your Avatar",
                    style = AppThemeTextStyle.Heading18D
                )
            },
            navigationIcon = {
                IconButton(
                    onClick = {
                        if (isBackButtonEnabled.value) {
                            isBackButtonEnabled.value = false
                            navActions.back()
                        }
                    },
                    enabled = isBackButtonEnabled.value
                ) {
                    Icon(
                        Icons.AutoMirrored.Filled.ArrowBack,
                        contentDescription = "返回"
                    )
                }
            },
        )

        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            if (uiState.isLoading) {
                LoadingState()
            } else if (uiState.error != null) {
                ErrorState(error = uiState.error!!)
            } else {
                if (uiState.showTaskLoadingCard && uiState.isTaskLoading) {
                    TaskLoadingCard(
                        progress = Progress(
                            current = uiState.progress.current,
                            total = uiState.progress.total
                        )
                    )
                } else if (uiState.previewedModelUrl == null) {
                    EmptyAvatarCard()
                } else {
                    uiState.previewedModelUrl?.let { url ->
                        AvatarCard(
                            imageUrl = url,
                            onDeleteClick = {
                                metric(
                                    SelectItem(
                                        EventItemListName.DeleteBtn,
                                        method = EventMethod.Click,
                                        items = listOf(
                                            EventItem(
                                                EventItemCategory.Avatar,
                                                runBlocking { UserDataStoreManager.getModelInfo().second },
                                            )
                                        )
                                    )
                                )
                                viewModel.deleteReplicaModel(uiState.previewedModelId!!)
                            }
                        )
                    }
                }
            }
        }

        Column(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f)
        ) {
            Text(
                text = "AI Avatars",
                color = Color(0xFF333333),
                style = MaterialTheme.typography.titleLarge,
                modifier = Modifier.padding(start = 16.dp, top = 8.dp, bottom = 8.dp)
            )

            AvatarHistory(
                replicaHistory = replicaHistory,
                currentModelId = currentModelId,
                previewedModelId = uiState.previewedModelId,
                onAvatarClick = { imageUrl, modelId ->
                    viewModel.updatePreviewedReplica(imageUrl, modelId)
                    viewModel.updateShowTaskLoadingCard(false)
                },
                hasMoreReplicaHistory = hasMoreReplicaHistory,
                onLoadMore = {
                    viewModel.loadMoreReplicaHistory()
                },
                isLoading = uiState.isLoadingMore,
                isTaskLoading = uiState.isTaskLoading,
                progress = Progress(
                    current = uiState.progress.current,
                    total = uiState.progress.total
                ),
                onAddAvatarClick = {
                    navActions.navigateToTryOnCreate()
                },
                onTaskLoadingCardClick = {
                    viewModel.updateShowTaskLoadingCard(true)
                    viewModel.clearPreviewedReplica()
                }
            )

            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
            ) {
                Button(
                    onClick = {
                        viewModel.updateReplica(
                            uiState.previewedModelId!!,
                            uiState.previewedModelUrl!!
                        )
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(48.dp),
                    shape = RoundedCornerShape(size = 4.dp),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = if (uiState.previewedModelId != null && currentModelId != uiState.previewedModelId && !uiState.isLoading)
                            Color(0xFF222222)
                        else
                            Color.White
                    ),
                    enabled = (uiState.previewedModelId != null && currentModelId != uiState.previewedModelId && !uiState.isLoading)
                ) {
                    Text(
                        text = "Set as default",
                        color = if (uiState.previewedModelId != null && currentModelId != uiState.previewedModelId && !uiState.isLoading)
                            Color.White
                        else
                            Color(0xFF222222),
                        style = AppThemeTextStyle.Body16H
                    )
                }
            }
        }
    }
}
