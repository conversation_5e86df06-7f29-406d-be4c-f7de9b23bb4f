package one.srp.gensmo.ui.screens.session.view.search

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.core.graphics.toColorInt
import androidx.hilt.navigation.compose.hiltViewModel
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import one.srp.core.analytics.events.SelectItem
import one.srp.core.analytics.events.ViewItemList
import one.srp.core.analytics.types.EventActionType
import one.srp.core.analytics.types.EventItem
import one.srp.core.analytics.types.EventItemCategory
import one.srp.core.analytics.types.EventItemListName
import one.srp.core.analytics.types.EventMethod
import one.srp.core.analytics.types.EventRefer
import one.srp.core.network.utils.JSON
import one.srp.gensmo.R
import one.srp.gensmo.data.model.ChatMessageRole
import one.srp.gensmo.data.model.CollectionType
import one.srp.gensmo.data.model.MoodboardContent
import one.srp.gensmo.data.model.MoodboardContentBlock
import one.srp.gensmo.data.model.MoodboardEntity
import one.srp.gensmo.data.model.ProductItem
import one.srp.gensmo.data.model.SearchItem
import one.srp.gensmo.data.model.SearchParams
import one.srp.gensmo.data.model.SearchQueryMessage
import one.srp.gensmo.data.model.SearchQueryMessageWrapper
import one.srp.gensmo.data.model.TryOnParams
import one.srp.gensmo.data.model.TryOnQueryMessage
import one.srp.gensmo.data.model.TryOnQueryMessageWrapper
import one.srp.gensmo.data.remote.MoodboardService
import one.srp.gensmo.data.store.UserDataStoreManager
import one.srp.gensmo.ui.components.collage.CollageSharePanel
import one.srp.gensmo.ui.components.collage.MoodboardRenderer
import one.srp.gensmo.ui.components.collage.SharePanelDrawer
import one.srp.gensmo.ui.components.exception.BaseError
import one.srp.gensmo.ui.components.loading.BaseLoading
import one.srp.gensmo.ui.components.navigate.TopBar
import one.srp.gensmo.ui.navigation.NavActions
import one.srp.gensmo.ui.screens.product.panel.alternatives.ProductAlternativesDrawer
import one.srp.gensmo.ui.screens.session.chat._components.SaveActionContainer
import one.srp.gensmo.ui.screens.session.chat._viewmodel.SessionChatViewModel
import one.srp.gensmo.ui.screens.session.view._components.ActionDropdownMenu
import one.srp.gensmo.ui.screens.session.view._components.AlternativesView
import one.srp.gensmo.ui.screens.session.view._components.CarouselContainer
import one.srp.gensmo.ui.screens.session.view._components.FeaturedProductView
import one.srp.gensmo.ui.screens.session.view._components.ShareButton
import one.srp.gensmo.ui.screens.tryon.create._components.LoginModal
import one.srp.gensmo.ui.theme.AppThemeColors
import one.srp.gensmo.ui.theme.AppThemeTextStyle
import one.srp.gensmo.utils.metrics.MetricViewModel
import one.srp.gensmo.utils.render.renderComposeToBitmap
import one.srp.gensmo.viewmodel.navigation.SharedNavViewModel
import one.srp.gensmo.viewmodel.search.CollageSearchViewModel
import one.srp.gensmo.viewmodel.tryon.CreateViewModel
import timber.log.Timber
import java.util.UUID

@Composable
fun CollageTaskViewScreen(
    navActions: NavActions = NavActions(),
    taskId: String? = null,
    initialIndex: Int = 0,
    sharedNavViewModel: SharedNavViewModel = hiltViewModel(),
    viewModel: SessionChatViewModel = hiltViewModel(),
    refer: EventRefer = EventRefer.CollageGen,
) {
    if (taskId == null) {
        return BaseError(onClick = { navActions.back() })
    }

    // 添加页面状态管理
    var isPageActive by remember { mutableStateOf(true) }
    
    // 安全的返回处理函数
    fun safeBack() {
        if (isPageActive) {
            try {
                navActions.back()
            } catch (e: Exception) {
                Timber.e(e, "Error during safe back navigation")
            }
        }
    }

    // 页面生命周期管理
    DisposableEffect(Unit) {
        isPageActive = true
        onDispose {
            isPageActive = false
        }
    }

    var result by remember { mutableStateOf<SearchItem?>(null) }

    LaunchedEffect(Unit) {
        val cached = sharedNavViewModel.getSearchItem(taskId)
        result = cached ?: run {
            val res = MoodboardService.api.getSearchTask(taskId)
            if (res.isSuccessful) {
                res.body()
            } else {
                null
            }
        }

        result?.moodboards?.forEach {
            try {
                it.parsedContent = JSON.decodeFromString<MoodboardContent>(it.content)
            } catch (e: Exception) {
                Timber.w(e)
            }
        }
    }

    var currentIndex by remember { mutableIntStateOf(initialIndex) }
    val currentMoodboard = remember(currentIndex, result) { result?.moodboards?.get(currentIndex) }

    val metricViewModel: MetricViewModel = hiltViewModel()
    val metric = metricViewModel.compatMetric(refer)
    val metricItemTask = remember(taskId, result) {
        EventItem(
            itemCategory = EventItemCategory.CollageGenTask,
            itemId = taskId,
            itemName = result?.reasoning,
        )
    }
    val metricItemCurrM = remember(currentMoodboard, result, currentIndex) {
        EventItem(
            itemCategory = EventItemCategory.GeneralCollage,
            itemId = currentMoodboard?.id,
            itemName = result?.reasoning,
            index = currentIndex,
        )
    }

    LaunchedEffect(Unit) {
        metric(
            SelectItem(
                itemListName = EventItemListName.Screen,
                method = EventMethod.PageView,
            )
        )
    }

    LaunchedEffect(currentMoodboard) {
        currentMoodboard?.let { moodboard ->
            metric(
                ViewItemList(
                    itemListName = EventItemListName.CollageListEntityList,
                    items = listOf(
                        metricItemTask, metricItemCurrM
                    ) + moodboard.products.mapIndexed { index, it ->
                        EventItem(
                            itemCategory = EventItemCategory.Product,
                            itemId = it.globalId,
                            itemName = it.brand,
                            index = index,
                        )
                    },
                )
            )
        }
    }

    var selectedProduct by remember { mutableStateOf<ProductItem?>(null) }
    fun onBlockClick(block: MoodboardContentBlock) {
        val product = currentMoodboard?.products?.find {
            block.globalId == it.globalId
        }
        product?.let {
            selectedProduct = it

            metric(
                SelectItem(
                    itemListName = EventItemListName.CollageListEntityListEntity,
                    method = EventMethod.Click,
                    actionType = EventActionType.EnterProductDetail,
                    items = listOf(
                        metricItemTask, metricItemCurrM, EventItem(
                            itemCategory = EventItemCategory.Product,
                            itemId = it.globalId,
                            itemName = it.brand,
                        )
                    ),
                )
            )
        }
    }

    fun tryOnProduct(product: ProductItem) {
        val message = TryOnQueryMessage(
            sessionId = "default",
            messageId = UUID.randomUUID().toString(),
            role = ChatMessageRole.User.value,
            visible = true,
            value = TryOnQueryMessageWrapper(
                tryonQuery = TryOnParams(
                    modelId = runBlocking { UserDataStoreManager.getModelInfo().second ?: "" },
                    products = listOf(product),
                    internalImageList = listOfNotNull(product.mainImage?.link),
                    isAsync = true,
                )
            )
        )
        viewModel.sendMessage(message)
        safeBack()
    }

    fun remixProduct(product: ProductItem) {
        val message = SearchQueryMessage(
            sessionId = "default",
            messageId = UUID.randomUUID().toString(),
            role = ChatMessageRole.User.value,
            visible = true,
            value = SearchQueryMessageWrapper(
                searchQuery = SearchParams(
                    debugLevel = 0,
                    query = "Complete the look",
                    budget = "",
                    isAsync = true,
                    route = "",
                    isPresetQuery = false,
                    moodboardVersion = "v2",
                    imageUrl = product.mainImage?.link ?: "",
                    useOnlineImageSeg = false,
                    specifiedProduct = product,
                )
            )
        )
        viewModel.sendMessage(message)
        safeBack()
    }

    var alternativesOpen by remember { mutableStateOf(false) }
    fun onMoreLook() {
        alternativesOpen = true

        metric(
            SelectItem(
                itemListName = EventItemListName.TryOnBtn,
                method = EventMethod.Click,
                actionType = EventActionType.TryOnSelect,
                items = listOf(metricItemTask, metricItemCurrM),
            )
        )
    }

    fun remixMoodboard(param: TryOnParams) {
        val message = TryOnQueryMessage(
            sessionId = "default",
            messageId = UUID.randomUUID().toString(),
            role = ChatMessageRole.User.value,
            visible = true,
            value = TryOnQueryMessageWrapper(tryonQuery = param),
        )
        viewModel.sendMessage(message)
        safeBack()
    }

    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    val collageSearchViewModel = hiltViewModel<CollageSearchViewModel>()
    suspend fun postMoodboard() {
        try {
            result?.let { result ->
                currentMoodboard?.let { moodboard ->
                    moodboard.parsedContent?.let {
                        val bitmap = renderComposeToBitmap(
                            context, it.width.toInt(), (it.height ?: (it.width / 0.7)).toInt()
                        ) {
                            MoodboardRenderer(
                                modifier = Modifier.fillMaxWidth(), item = it, offscreen = true
                            )
                        }
                        collageSearchViewModel.updatePreviewFromBitmap(
                            moodboard, result, bitmap
                        )
                    }
                }
            }
        } catch (e: Exception) {
            Timber.w(e)
        }
    }

    fun saveMoodboard(targetState: Boolean) {
        metric(
            SelectItem(
                itemListName = EventItemListName.SaveBtn,
                method = EventMethod.Click,
                actionType = if (targetState) EventActionType.Save else EventActionType.Unsave,
                items = listOf(metricItemTask, metricItemCurrM),
            )
        )

        // 只在保存（而非取消保存）时才执行postMoodboard逻辑
        if (targetState) {
            coroutineScope.launch {
                postMoodboard()
            }
        }
    }

    var postLoading by remember { mutableStateOf(false) }
    fun postFeed() {
        coroutineScope.launch {
            postLoading = true

            metric(
                SelectItem(
                    itemListName = EventItemListName.InitializePostBtn,
                    method = EventMethod.Click,
                    actionType = EventActionType.InitializePost,
                    items = listOf(metricItemTask),
                )
            )

            try {
                postMoodboard()
            } catch (e: Exception) {
                Timber.w(e)
            }
            postLoading = false

            currentMoodboard?.let {
                navActions.navigateToPostEditor(it.id, "collage")
            }
        }
    }

    Scaffold(topBar = {
        TopBar(immersive = true, transparent = true, onBack = { safeBack() }, action = {
            ActionMenu(item = result, moodboard = currentMoodboard)
        })
    }) { paddingValues ->
        Column(
            modifier = Modifier.padding(
                top = WindowInsets.statusBars.asPaddingValues().calculateTopPadding(),
                bottom = paddingValues.calculateBottomPadding()
            )
        ) {
            result?.let { result ->
                LaunchedEffect(Unit) {
                    metric(
                        ViewItemList(
                            itemListName = EventItemListName.CollageList,
                            items = listOf(metricItemTask) + (result.moodboards?.mapIndexed { index, it ->
                                EventItem(
                                    itemCategory = EventItemCategory.GeneralCollage,
                                    itemId = it.id,
                                    itemName = result.reasoning,
                                    index = index,
                                )
                            } ?: emptyList()),
                        ))
                }

                Column {
                    // 可滚动的内容区域
                    Column(
                        modifier = Modifier
                            .weight(1f)
                            .verticalScroll(rememberScrollState())
                    ) {
                        // Moodboard 展示区域
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(bottom = 16.dp)
                        ) {
                            CarouselContainer(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .heightIn(min = 400.dp, max = 600.dp),
                                items = result.moodboards ?: emptyList(),
                                initialPage = initialIndex,
                                onIndexChange = { currentIndex = it },
                                indicator = true,
                            ) { moodboard ->
                                val backgroundColor = remember(moodboard) {
                                    moodboard.parsedContent?.background?.let { bg ->
                                        if (bg.startsWith("#")) Color(
                                            bg.trim().toColorInt()
                                        ) else Color.White
                                    } ?: Color.White
                                }

                                Box(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .background(backgroundColor)
                                ) {
                                    MoodboardRenderer(
                                        modifier = Modifier.fillMaxWidth(),
                                        item = moodboard.parsedContent
                                            ?: JSON.decodeFromString<MoodboardContent>(moodboard.content),
                                        onItemClick = { onBlockClick(it) },
                                        animate = true,
                                        interactive = true,
                                        showTags = true,
                                    )
                                }
                            }

                            ProductView(
                                item = selectedProduct,
                                productMap = result.searchProductList,
                                open = selectedProduct != null,
                                onClose = { selectedProduct = null },
                                onTryOn = { tryOnProduct(it) },
                                onRemix = { remixProduct(it) },
                            )
                        }

                        // 显示当前 moodboard 的精选产品列表
                        currentMoodboard?.let { moodboard ->
                            if (moodboard.products.isNotEmpty()) {
                                FeaturedProductView(
                                    refer = refer,
                                    products = moodboard.products,
                                    onProductClick = { product ->
                                        // 备用方案：如果外部跳转失败，显示内部产品详情
                                        selectedProduct = product
                                    },
                                    collageGenTaskId = taskId,
                                    generalCollageId = currentMoodboard?.id
                                )
                            }
                        }
                    }

                    // 固定在底部的操作按钮
                    BottomAction(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        item = currentMoodboard,
                        onMoreLook = { onMoreLook() },
                        onSave = { saveMoodboard(it) },
                        onPost = { postFeed() },
                        postLoading = postLoading,
                    )

                    AlternativesView(
                        open = alternativesOpen,
                        onClose = { alternativesOpen = false },
                        moodboardId = currentMoodboard?.id,
                        onCommit = { remixMoodboard(it) })
                }
            } ?: run {
                BaseLoading(modifier = Modifier.fillMaxSize())
            }
        }
    }
}

@Composable
private fun ActionMenu(
    item: SearchItem?, moodboard: MoodboardEntity?, refer: EventRefer = EventRefer.CollageGen,
) {
    var shareOpen by remember { mutableStateOf(false) }

    val metricViewModel: MetricViewModel = hiltViewModel()
    val metric = metricViewModel.compatMetric(refer)

    fun clickShare() {
        shareOpen = true

        metric(
            SelectItem(
                itemListName = EventItemListName.ShareBtn,
                method = EventMethod.Click,
                actionType = EventActionType.EnterSharePage,
                items = listOf(
                    EventItem(
                        itemCategory = EventItemCategory.CollageGenTask,
                        itemId = item?.taskId,
                        itemName = item?.reasoning,
                    ),
                    EventItem(
                        itemCategory = EventItemCategory.GeneralCollage,
                        itemId = moodboard?.id,
                        itemName = item?.reasoning,
                    )
                ),
            )
        )
    }

    ActionDropdownMenu(menu = { onClose ->
        ShareButton(onClick = {
            clickShare()
            onClose()
        })
        // TODO: dev
//        DownloadButton()
//        FeedbackButton()
    }) {
        SharePanelDrawer(open = shareOpen, onClose = { shareOpen = false }) {
            CollageSharePanel(moodboard, item)
        }
    }
}

@Composable
private fun ProductView(
    item: ProductItem?,
    productMap: Map<String, List<ProductItem>>? = null,
    open: Boolean = false,
    onClose: () -> Unit = {},
    onTryOn: (ProductItem) -> Unit = {},
    onRemix: (ProductItem) -> Unit = {},
) {
    ProductAlternativesDrawer(
        open = open,
        onClose = { onClose() },
        onTryOn = {
            onTryOn(it)
            onClose()
        },
        onRemix = {
            onRemix(it)
            onClose()
        },
        selected = item,
        products = productMap?.get(item?.id),
        replaceable = false,
    )
}

@Composable
private fun BottomAction(
    modifier: Modifier = Modifier,
    item: MoodboardEntity?,
    onSave: (Boolean) -> Unit = {},
    onMoreLook: () -> Unit = {},
    onPost: () -> Unit = {},
    postLoading: Boolean = false,
) {
    val context = LocalContext.current
    val createViewModel: CreateViewModel = hiltViewModel(key = "CollageTaskView")
    val uiState by createViewModel.uiState.collectAsState()

    fun clickPost() {
        if (createViewModel.isUserLoggedIn()) {
            onPost()
        } else {
            createViewModel.updateLoginDialog(true)
            createViewModel.setLoginSource("default_avatar")
        }
    }

    Row(horizontalArrangement = Arrangement.spacedBy(16.dp), modifier = modifier) {
        item?.let { item ->
            SaveActionContainer(
                type = CollectionType.Collage,
                id = item.id,
                initialState = item.isFavorited == true,
                onPreAction = { onSave(it) },
                onAction = { item.isFavorited = it }) { state, onClick ->
                Button(
                    onClick = { onClick() },
                    colors = ButtonDefaults.buttonColors(
                        MaterialTheme.colorScheme.surface, MaterialTheme.colorScheme.onSurface
                    ),
                    shape = MaterialTheme.shapes.medium,
                    contentPadding = PaddingValues(0.dp),
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Image(
                            painterResource(if (state) R.drawable.icon_saved_black else R.drawable.icon_saved_line),
                            null,
                            modifier = Modifier.size(24.dp)
                        )
                    }
                }
            }
        }

        Button(
            onClick = onMoreLook,
            modifier = Modifier.weight(2f),
            colors = ButtonDefaults.buttonColors(
                MaterialTheme.colorScheme.surface, MaterialTheme.colorScheme.onSurface
            ),
            shape = MaterialTheme.shapes.medium,
        ) {
            Text(stringResource(R.string.text_try_more), style = AppThemeTextStyle.Body16H)
        }

        Button(
            onClick = { clickPost() },
            modifier = Modifier.weight(2f),
            shape = MaterialTheme.shapes.medium,
        ) {
            if (postLoading) {
                BaseLoading(
                    strokeWidth = 2.dp,
                    modifier = Modifier.size(24.dp),
                    color = AppThemeColors.White
                )
            } else {
                Text(stringResource(R.string.text_post), style = AppThemeTextStyle.Body16H)
            }
        }
    }

    if (uiState.showLoginDialog) {
        LoginModal(
            onDismiss = { createViewModel.updateLoginDialog(false) },
            isLoading = uiState.isLoading,
            onGoogleLogin = {
                createViewModel.loginWithGoogle(
                    context = context, onSuccess = {
                        onPost()
                    })
                createViewModel.updateLoginDialog(false)
            },
            onAppleLogin = {
                createViewModel.loginWithApple(
                    context = context, onSuccess = {
                        onPost()
                    })
                createViewModel.updateLoginDialog(false)
            })
    }
}
