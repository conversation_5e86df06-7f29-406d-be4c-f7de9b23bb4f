package one.srp.gensmo.ui.screens.detail._components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.clickable
import androidx.compose.foundation.lazy.staggeredgrid.LazyVerticalStaggeredGrid
import androidx.compose.foundation.lazy.staggeredgrid.StaggeredGridCells
import androidx.compose.foundation.lazy.staggeredgrid.rememberLazyStaggeredGridState
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalWindowInfo
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.paging.LoadState
import androidx.paging.compose.collectAsLazyPagingItems
import one.srp.gensmo.R
import one.srp.gensmo.data.model.FeedItem
import one.srp.gensmo.ui.screens.feed._components.FeedCard
import one.srp.gensmo.ui.theme.AppThemeTextStyle
import one.srp.gensmo.viewmodel.feed.FeedDetailViewModel
@Composable
fun DetailSimilar(
    modifier: Modifier = Modifier,
    itemId: String,
    onItemClick: (FeedItem) -> Unit = {},
    feedDetailViewModel: FeedDetailViewModel,
) {
    val similarPagingItems = feedDetailViewModel.getSimilarPagingFlow(itemId).collectAsLazyPagingItems()
    
    val windowInfo = LocalWindowInfo.current
    val density = LocalDensity.current
    val screenHeight = with(density) { windowInfo.containerSize.height.toDp() }
    val topBarHeight = 64.dp // TopBar 的默认高度
    val gridHeight = screenHeight - topBarHeight
    
    val lazyGridState = rememberLazyStaggeredGridState()

    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 16.dp)
    ) {
        // 只有在有数据或正在加载时才显示标题
        if (similarPagingItems.itemCount > 0 || similarPagingItems.loadState.refresh is LoadState.Loading) {
            Text(
                text = stringResource(R.string.text_more_like_this),
                style = AppThemeTextStyle.Heading20D,
                modifier = Modifier.padding(bottom = 12.dp)
            )
        }

        if (similarPagingItems.itemCount == 0 && similarPagingItems.loadState.refresh is LoadState.Loading) {
            // 初始加载状态
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 20.dp),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        } else if (similarPagingItems.itemCount == 0 && similarPagingItems.loadState.refresh is LoadState.Error) {
            // 初始加载失败
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 20.dp)
                    .clickable {
                        similarPagingItems.refresh()
                    },
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "Failed to load, tap to retry",
                    style = AppThemeTextStyle.Body14H,
                    color = Color.Gray
                )
            }
        } else {
            // 显示列表
            LazyVerticalStaggeredGrid(
                columns = StaggeredGridCells.Fixed(2),
                state = lazyGridState,
                modifier = Modifier
                    .fillMaxWidth()
                    .height(gridHeight),
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalItemSpacing = 8.dp
            ) {
                items(similarPagingItems.itemCount) { index ->
                    similarPagingItems[index]?.let { item ->
                        var liked by remember(item.moodboardId) { mutableStateOf(item.isLiked) }
                        
                        FeedCard(
                            modifier = Modifier.fillMaxWidth(),
                            item = item,
                            onItemClick = { onItemClick(item) },
                            liked = liked,
                            onLikeClick = { 
                                feedDetailViewModel.toggleLike(item) { newLiked, _ ->
                                    liked = newLiked
                                }
                            }
                        )
                    }
                }
                
                // 加载更多状态
                when (val appendState = similarPagingItems.loadState.append) {
                    is LoadState.Loading -> {
                        item {
                            Box(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = 16.dp),
                                contentAlignment = Alignment.Center
                            ) {
                                CircularProgressIndicator()
                            }
                        }
                    }
                    is LoadState.Error -> {
                        item {
                            Box(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = 16.dp)
                                    .clickable {
                                        similarPagingItems.retry()
                                    },
                                contentAlignment = Alignment.Center
                            ) {
                                Text(
                                    text = "Failed to load, tap to retry",
                                    style = AppThemeTextStyle.Body14H,
                                    color = Color.Gray
                                )
                            }
                        }
                    }
                    is LoadState.NotLoading -> {
                        if (appendState.endOfPaginationReached && similarPagingItems.itemCount > 0) {
                            item {
                                Box(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(vertical = 16.dp),
                                    contentAlignment = Alignment.Center
                                ) {
                                    Text(
                                        text = "No more content",
                                        style = AppThemeTextStyle.Body14H,
                                        color = Color.Gray
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
