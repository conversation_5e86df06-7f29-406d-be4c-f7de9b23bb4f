package one.srp.gensmo.ui.screens.detail._components

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import one.srp.gensmo.data.model.CommentResponse
import one.srp.gensmo.data.model.PostCommentReq
import one.srp.gensmo.data.remote.CommentService
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class DetailCommentViewModel @Inject constructor(
) : ViewModel() {

    private val _commentList = MutableStateFlow(emptyList<CommentResponse>())
    val commentList = _commentList.asStateFlow()

    fun getCommentList(postId: String) {
        viewModelScope.launch(Dispatchers.IO) {
            try {
                val res = CommentService.api.getPostComments(postId)
                if (res.isSuccessful) {
                    _commentList.value = res.body()?.comments ?: emptyList()
                }
            } catch (e: Exception) {
                Timber.w(e)
            }
        }
    }

    suspend fun postComment(
        content: String,
        postId: String,
        userId: String,
        parentCommentId: String? = null,
    ) {
        try {
            val res = CommentService.api.postComment(
                PostCommentReq(
                    content = content,
                    userId = userId,
                    postId = postId,
                    parentCommentId = parentCommentId,
                )
            )
            if (res.isSuccessful) {
                viewModelScope.launch(Dispatchers.IO) {
                    getCommentList(postId)
                }
            }
        } catch (e: Exception) {
            Timber.w(e)
        }
    }
}
