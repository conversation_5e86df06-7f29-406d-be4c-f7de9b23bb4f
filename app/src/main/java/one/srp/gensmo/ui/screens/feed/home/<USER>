package one.srp.gensmo.ui.screens.feed.home

import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.material3.rememberTopAppBarState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import coil3.compose.AsyncImage
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking
import one.srp.core.analytics.events.SelectItem
import one.srp.core.analytics.events.ViewItemList
import one.srp.core.analytics.types.EventActionType
import one.srp.core.analytics.types.EventItem
import one.srp.core.analytics.types.EventItemCategory
import one.srp.core.analytics.types.EventItemListName
import one.srp.core.analytics.types.EventMethod
import one.srp.core.analytics.types.EventRefer
import one.srp.gensmo.R
import one.srp.gensmo.data.model.ChatMessage
import one.srp.gensmo.data.model.ChatMessageRole
import one.srp.gensmo.data.model.FeedItem
import one.srp.gensmo.data.model.SearchParams
import one.srp.gensmo.data.model.SearchQueryMessage
import one.srp.gensmo.data.model.SearchQueryMessageWrapper
import one.srp.gensmo.data.repository.utils.BaseState
import one.srp.gensmo.viewmodel.notification.NotificationStateViewModel
import one.srp.gensmo.ui.components.camera.CameraPicker
import one.srp.gensmo.ui.components.navigate.BottomNavigationBar
import one.srp.gensmo.ui.components.search.SearchPanel
import one.srp.gensmo.ui.navigation.NavActions
import one.srp.gensmo.ui.screens.feed.home._components.FeedFlow
import one.srp.gensmo.ui.screens.feed.home._components.SearchCard
import one.srp.gensmo.ui.theme.AppThemeTextStyle
import one.srp.gensmo.utils.metrics.MetricViewModel
import one.srp.gensmo.viewmodel.search.SearchExtensionViewModel
import one.srp.gensmo.data.store.UserDataStoreManager
import timber.log.Timber
import java.util.UUID

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FeedHomeScreen(
    navActions: NavActions = NavActions(),
    openDetail: (FeedItem) -> Unit = {},
    createSession: (ChatMessage) -> Unit = {},
) {
    val metricViewModel: MetricViewModel = hiltViewModel()
    val metric = metricViewModel.compatMetric(EventRefer.Home)
    LaunchedEffect(Unit) {
        metric(
            SelectItem(
                itemListName = EventItemListName.Screen, method = EventMethod.PageView
            )
        )
    }

    val topAppBarState = rememberTopAppBarState()
    val topAppBarScrollBehavior = TopAppBarDefaults.enterAlwaysScrollBehavior(topAppBarState)

    val searchExtensionViewModel: SearchExtensionViewModel = hiltViewModel()
    val homeInfo by searchExtensionViewModel.homeInfo.collectAsState()

    // 通知状态ViewModel - 用于获取未读通知数量
    val notificationStateViewModel: NotificationStateViewModel = hiltViewModel()
    val unreadCount by notificationStateViewModel.unreadCount.collectAsState()

    // 用户登录状态 - 用于UI显示
    var isUserLoggedIn by remember { mutableStateOf(false) }

    // 获取首页信息
    LaunchedEffect(Unit) {
        searchExtensionViewModel.fetchHomeInfo()
        // 检查用户登录状态
        isUserLoggedIn = runBlocking { 
            UserDataStoreManager.isUserLoggedIn() 
        }
    }


    var searchPanelVisible by remember { mutableStateOf(false) }
    val openSearch = {
        searchPanelVisible = true
    }
    var placeholder by remember { mutableStateOf("Describe the style or occasion you want to explore...") }
    val onSearch: (String, String, String?, String?) -> Unit = { q, img, stylesList, budget ->
        if (q.trim().isNotEmpty()) {
            Timber.d("onSearch: $q, $img, $stylesList, $budget")
            createSession(
                SearchQueryMessage(
                    sessionId = "default",
                    messageId = UUID.randomUUID().toString(),
                    role = ChatMessageRole.User.value,
                    visible = true,
                    value = SearchQueryMessageWrapper(
                        searchQuery = SearchParams(
                            query = q,
                            imageUrl = img,
                            debugLevel = 0,
                            budget = budget ?: "",
                            isAsync = true,
                            route = "",
                            isPresetQuery = false,
                            moodboardVersion = "v2",
                            inspoLabel = (stylesList?.split(",")?.map { it.trim() }
                                ?.filter { it.isNotEmpty() } ?: emptyList())
                        )
                    )
                )
            )
        }
    }

    var showCameraPicker by remember { mutableStateOf(false) }
    var capturedImageUri by remember { mutableStateOf<String?>(null) }
    var capturedSearchText by remember { mutableStateOf("") }

    val closeSearch = {
        searchPanelVisible = false
        capturedSearchText = ""
        capturedImageUri = null
    }

    fun onFeedScroll() {
        metric(
            SelectItem(
                itemListName = EventItemListName.DualColumnFeedForYouList,
                method = EventMethod.Swipe,
            )
        )
    }

    fun genEventItemByFeedType(item: FeedItem): List<EventItem> {
        val baseItem = when (item.type) {
            "tryon" -> EventItem(
                itemCategory = EventItemCategory.TryOnCollage,
                itemId = item.tryOnTaskId,
                itemName = item.reasoning
            )
            else -> EventItem(
                itemCategory = EventItemCategory.GeneralCollage,
                itemId = item.moodboardId,
                itemName = item.reasoning
            )
        }
        
        val resultList = mutableListOf(baseItem)
        
        // 添加推荐项目
        if (item.recommendId?.isNotEmpty() == true) {
            resultList.add(
                EventItem(
                    itemCategory = EventItemCategory.Reco,
                    itemId = item.recommendId,
                    itemName = item.reasoning
                )
            )
        }
        
        return resultList
    }

    fun genBaseEventItemByFeedType(item: FeedItem): EventItem {
        return when (item.type) {
            "tryon" -> EventItem(
                itemCategory = EventItemCategory.TryOnCollage,
                itemId = item.tryOnTaskId,
                itemName = item.reasoning
            )
            else -> EventItem(
                itemCategory = EventItemCategory.GeneralCollage,
                itemId = item.moodboardId,
                itemName = item.reasoning
            )
        }
    }

    fun onFeedLoadMore(list: List<FeedItem> = emptyList()) {
        metric(
            SelectItem(
                itemListName = EventItemListName.DualColumnFeedForYouList,
                method = EventMethod.Swipe,
                actionType = EventActionType.LoadMore,
            )
        )
        
        val baseItems = list.map { item -> genBaseEventItemByFeedType(item) }
        val items = mutableListOf<EventItem>()
        
        // 添加一个单独的 reco 项目到最前面
        val firstItemWithRecommendId = list.firstOrNull { it.recommendId?.isNotEmpty() == true }
        if (firstItemWithRecommendId != null) {
            items.add(
                EventItem(
                    itemCategory = EventItemCategory.Reco,
                    itemId = firstItemWithRecommendId.recommendId,
                    itemName = firstItemWithRecommendId.reasoning
                )
            )
        }
        
        // 添加基础项目
        items.addAll(baseItems)
        
        metric(
            ViewItemList(
                itemListName = EventItemListName.DualColumnFeedForYouList,
                items = items
            )
        )
    }

    fun clickFeedItem(item: FeedItem) {
        metric(
            SelectItem(
                itemListName = EventItemListName.DualColumnFeedForYouListItemCover,
                method = EventMethod.Click,
                actionType = EventActionType.EnterFeedDetail,
                items = genEventItemByFeedType(item)
            )
        )
        openDetail(item)
    }

    fun onFeedItemView(item: FeedItem) {
        metric(
            SelectItem(
                itemListName = EventItemListName.DualColumnFeedForYouList,
                method = EventMethod.TrueViewTrigger,
                items = genEventItemByFeedType(item)
            )
        )
    }

    fun clickFeedLike(item: FeedItem, newLike: Boolean) {

    }

    Scaffold(containerColor = MaterialTheme.colorScheme.surface, topBar = {
        TopAppBar(
            scrollBehavior = topAppBarScrollBehavior,
            modifier = Modifier.animateContentSize(),
            colors = TopAppBarDefaults.topAppBarColors(
                MaterialTheme.colorScheme.surface,
            ),
            title = {
                Text(
                    stringResource(R.string.app_name).uppercase(),
                    style = AppThemeTextStyle.Heading24D
                )
            },
            actions = {
                Row(
                    modifier = Modifier.padding(end = 4.dp),
                    horizontalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    // 当homeInfo成功获取且miniAppActiveEntrance字段存在时，显示活动入口
                    when (val currentHomeInfo = homeInfo) {
                        is BaseState.Success -> {
                            val activity = currentHomeInfo.data.miniActiveEntrance?.firstOrNull()
                            if (activity?.iconLink?.isNotEmpty() == true) {
                                Box(
                                    modifier = Modifier
                                        .size(32.dp)
                                        .clip(RoundedCornerShape(8.dp))
                                        .clickable {
                                            // 点击活动入口，在应用内打开webview
                                            activity.entranceLink?.let { url ->
                                                try {
                                                    navActions.navigateToActivity(url)
                                                    // 添加活动点击埋点
                                                    metric(
                                                        SelectItem(
                                                            itemListName = EventItemListName.TopNavibarActivityEntry,
                                                            method = EventMethod.Click,
                                                            actionType = EventActionType.EntryActivity,
                                                            items = listOf(
                                                                EventItem(
                                                                    itemCategory = EventItemCategory.Activity,
                                                                )
                                                            ),
                                                        )
                                                    )
                                                } catch (e: Exception) {
                                                    Timber.e(e, "导航到活动页面失败: $url")
                                                }
                                            }
                                        },
                                    contentAlignment = Alignment.Center
                                ) {
                                    AsyncImage(
                                        model = activity.iconLink,
                                        contentDescription = "活动入口",
                                        modifier = Modifier.size(32.dp),
                                        contentScale = ContentScale.Fit
                                    )
                                }
                            }
                        }

                        else -> {
                            // 其他状态下不显示活动入口
                        }
                    }
                    
                    // Notification图标 - 只有用户登录时才显示
                    if (isUserLoggedIn) {
                        Box(
                            modifier = Modifier.size(32.dp)
                        ) {
                            IconButton(
                                onClick = {
                                    // 添加通知按钮点击埋点
                                    metric(
                                        SelectItem(
                                            itemListName = EventItemListName.NotificationBtn,
                                            method = EventMethod.Click
                                        )
                                    )
                                    navActions.navigateToNotification()
                                },
                                modifier = Modifier.size(32.dp)
                            ) {
                                Icon(
                                    painter = painterResource(id = R.drawable.icon_notification),
                                    contentDescription = "通知",
                                    modifier = Modifier.size(24.dp),
                                    tint = MaterialTheme.colorScheme.onSurface
                                )
                            }
                            
                            // 未读通知红点
                            if (unreadCount > 0) {
                                Box(
                                    modifier = Modifier
                                        .size(8.dp)
                                        .clip(CircleShape)
                                        .background(MaterialTheme.colorScheme.error)
                                        .align(Alignment.TopEnd)
                                )
                            }
                        }
                    }
                }
            }
        )
    }, bottomBar = {
        BottomNavigationBar(
            navActions = navActions,
            refer = EventRefer.Home,
            onActionClick = { openSearch() })
    }) { paddingValues ->
        Column(
            modifier = Modifier
                .padding(top = paddingValues.calculateTopPadding())
                .background(MaterialTheme.colorScheme.surface)
        ) {
            Box(
                modifier = Modifier
                    .background(Color.Transparent)
                    .padding(start = 4.dp, end = 4.dp, top = 0.dp, bottom = 8.dp)
            ) {
                SearchTrigger(
                    viewModel = searchExtensionViewModel,
                    placeholder = placeholder,
                    onSearch = {
                        metric(
                            SelectItem(
                                itemListName = EventItemListName.TopNavibarSearchInputBox,
                                method = EventMethod.Click,
                                actionType = EventActionType.InitializeSearch
                            )
                        )
                        openSearch()
                    },
                    onShuffle = { placeholder = it })

                SearchPanel(
                    placeholderText = placeholder,
                    isVisible = searchPanelVisible && !showCameraPicker,
                    onDismiss = { closeSearch() },
                    viewModel = searchExtensionViewModel,
                    imageUrl = capturedImageUri,
                    searchQueryText = capturedSearchText,
                    onCameraRequest = { showCameraPicker = true },
                    onSearch = onSearch,
                    onUpdateQuery = { query -> capturedSearchText = query },
                    navActions = navActions
                )
            }

            FeedFlow(
                modifier = Modifier.nestedScroll(topAppBarScrollBehavior.nestedScrollConnection),
                onItemClick = { clickFeedItem(it) },
                onLikeClick = { item, state -> clickFeedLike(item, state) },
                onScroll = { onFeedScroll() },
                onLoadMore = { onFeedLoadMore(it) },
                onItemView = { onFeedItemView(it) })

            Spacer(modifier = Modifier.height(paddingValues.calculateBottomPadding()))
        }
    }

    if (showCameraPicker) {
        CameraPicker(onPhotoTaken = { uri, searchQueryWords ->
            capturedImageUri = uri.toString()
            capturedSearchText += searchQueryWords
            showCameraPicker = false
        }, onMiss = {
            showCameraPicker = false
            capturedImageUri = null
        })
    }
}

@Composable
fun SearchTrigger(
    viewModel: SearchExtensionViewModel = hiltViewModel(),
    placeholder: String = "Describe the style or occasion you want to explore...",
    onSearch: () -> Unit = {},
    onShuffle: (String) -> Unit = {},
) {
    val imaginationResult by viewModel.imaginationResult.collectAsState()

    LaunchedEffect(Unit) {
        viewModel.getImaginationQuery()
    }

    LaunchedEffect(imaginationResult) {
        if (imaginationResult.isNotEmpty()) {
            onShuffle(imaginationResult.random())

            while (true) {
                delay(5000)
                if (imaginationResult.isNotEmpty()) {
                    onShuffle(imaginationResult.random())
                }
            }
        }
    }

    SearchCard(text = placeholder, onClick = { onSearch() })
}
