package one.srp.gensmo.ui.screens.detail._components

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowOutward
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.IconButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import coil3.compose.AsyncImage
import one.srp.gensmo.R
import one.srp.gensmo.data.model.ProductItem
import one.srp.gensmo.ui.theme.AppThemeColors
import one.srp.gensmo.ui.theme.AppThemeTextStyle

@Composable
fun ProductCard(
    modifier: Modifier = Modifier,
    item: ProductItem,
    onItemClick: (ProductItem) -> Unit = {},
    saved: Boolean = false,
    onSave: () -> Unit = {},
) {
    Box(modifier = modifier) {
        Column(
            modifier = Modifier.clickable { onItemClick(item) },
        ) {
            Column(
                modifier = Modifier
                    .aspectRatio(0.8f)
                    .background(MaterialTheme.colorScheme.surface)
                    .clip(MaterialTheme.shapes.small),
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                Column(modifier = Modifier.padding(2.dp)) {
                    AsyncImage(item.mainImage?.link, null)
                }
            }

            Column(
                modifier = Modifier.padding(4.dp, 6.dp),
                verticalArrangement = Arrangement.spacedBy(10.dp)
            ) {
                Text(
                    text = when {
                        item.brand == null || item.brand.isEmpty() -> "Curated pick"
                        item.brandParentName != null && item.brandParentName.isNotEmpty() -> "${item.brand} by ${item.brandParentName}"
                        else -> item.brand
                    },
                    style = AppThemeTextStyle.Heading16H,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                )

                item.title?.let { title ->
                    Text(
                        text = title,
                        style = AppThemeTextStyle.Body12LightH.copy(AppThemeColors.Gray700),
                        minLines = 2,
                        maxLines = 2,
                        overflow = TextOverflow.Ellipsis,
                    )
                }

                Row(
                    modifier = Modifier
                        .background(
                            color = Color(0xFFF5F5F5),
                            shape = RoundedCornerShape(size = 4.dp)
                        )
                        .padding(start = 4.dp, top = 4.dp, end = 4.dp, bottom = 4.dp),
                    horizontalArrangement = Arrangement.spacedBy(4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    AsyncImage(
                        model = item.sourceIcon, 
                        contentDescription = null, 
                        modifier = Modifier.size(12.dp)
                    )
                    Text(
                        text = item.buyboxWinner?.price?.raw ?: "", 
                        style = AppThemeTextStyle.Body11LightH
                    )
                    Icon(
                        Icons.Default.ArrowOutward, 
                        null, 
                        modifier = Modifier.size(16.dp),
                        tint = Color(0xFF6F6F6F)
                    )
                }
            }
        }

        Box(
            modifier = Modifier
                .align(Alignment.TopEnd)
                .padding(8.dp)
        ) {
            IconButton(
                modifier = Modifier.size(32.dp), colors = IconButtonDefaults.iconButtonColors(
                    MaterialTheme.colorScheme.background
                ), onClick = { onSave() }) {
                Image(
                    painterResource(
                        if (saved) R.drawable.icon_saved_black
                        else R.drawable.icon_saved_line
                    ), null
                )
            }
        }
    }
}
