package one.srp.gensmo.viewmodel.user

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import one.srp.gensmo.data.model.FollowerItem
import one.srp.gensmo.data.remote.CommunityService
import one.srp.gensmo.ui.components.action.FollowStatus
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class FollowingListViewModel @Inject constructor() : ViewModel() {

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _following = MutableStateFlow<List<FollowerItem>>(emptyList())
    val following: StateFlow<List<FollowerItem>> = _following.asStateFlow()

    private val _totalCount = MutableStateFlow(0)

    private val _currentPage = MutableStateFlow(1)

    private val _hasMore = MutableStateFlow(true)
    val hasMore: StateFlow<Boolean> = _hasMore.asStateFlow()

    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()

    // 添加关注状态管理
    private val _followStatusMap = MutableStateFlow<Map<String, FollowStatus>>(emptyMap())
    val followStatusMap: StateFlow<Map<String, FollowStatus>> = _followStatusMap.asStateFlow()

    private var currentUserId: String? = null
    private val pageSize = 10

    fun loadFollowing(userId: String, page: Int = 1) {
        if (page == 1) {
            currentUserId = userId
            _following.value = emptyList()
            _currentPage.value = 1
            _hasMore.value = true
            _followStatusMap.value = emptyMap() // 重置关注状态
        }

        viewModelScope.launch {
            try {
                _isLoading.value = true
                _error.value = null

                val response = CommunityService.api.getFollowingList(
                    userId = userId,
                    page = page,
                    pageSize = pageSize
                )

                if (response.isSuccessful) {
                    val followingResponse = response.body()
                    followingResponse?.let { data ->
                        if (page == 1) {
                            _following.value = data.data
                        } else {
                            _following.value = _following.value + data.data
                        }
                        
                        // 更新关注状态映射
                        val newFollowStatusMap = mutableMapOf<String, FollowStatus>()
                        data.data.forEach { item ->
                            val status = when (item.followStatus) {
                                FollowStatus.Followed.value -> FollowStatus.Followed
                                FollowStatus.Mutual.value -> FollowStatus.Mutual
                                FollowStatus.FollowedBack.value -> FollowStatus.FollowedBack
                                FollowStatus.NotFollowed.value -> FollowStatus.NotFollowed
                                else -> null
                            }
                            if (status != null) {
                                newFollowStatusMap[item.uid] = status
                            }
                        }
                        
                        if (page == 1) {
                            _followStatusMap.value = newFollowStatusMap
                        } else {
                            _followStatusMap.value = _followStatusMap.value + newFollowStatusMap
                        }
                        
                        _totalCount.value = data.pagination.total
                        _currentPage.value = data.pagination.page
                        _hasMore.value = data.pagination.page < data.pagination.totalPages
                    }
                } else {
                    _error.value = "获取following列表失败"
                    Timber.e("Failed to load following: ${response.code()}")
                }
            } catch (e: Exception) {
                _error.value = "网络错误: ${e.message}"
                Timber.e(e, "Error loading following")
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun loadMore() {
        val userId = currentUserId ?: return
        val nextPage = _currentPage.value + 1
        
        if (_hasMore.value && !_isLoading.value) {
            loadFollowing(userId, nextPage)
        }
    }

    // 更新特定用户的关注状态
    fun updateFollowStatus(userId: String, followStatus: FollowStatus) {
        val currentMap = _followStatusMap.value.toMutableMap()
        currentMap[userId] = followStatus
        _followStatusMap.value = currentMap
    }
} 