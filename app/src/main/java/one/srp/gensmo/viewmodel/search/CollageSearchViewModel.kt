package one.srp.gensmo.viewmodel.search

import android.graphics.Bitmap
import androidx.compose.runtime.mutableStateMapOf
import androidx.core.net.toUri
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.toRequestBody
import one.srp.core.network.utils.JSON
import one.srp.gensmo.data.model.MoodboardContent
import one.srp.gensmo.data.model.MoodboardEntity
import one.srp.gensmo.data.model.PostSearchItem
import one.srp.gensmo.data.model.ProductItem
import one.srp.gensmo.data.model.SearchInspoItem
import one.srp.gensmo.data.model.SearchItem
import one.srp.gensmo.data.model.SearchV2Request
import one.srp.gensmo.data.model.TaskStatus
import one.srp.gensmo.data.remote.ImageService
import one.srp.gensmo.data.remote.MoodboardService
import one.srp.gensmo.data.remote.SuggestService
import one.srp.gensmo.data.remote.UserService
import one.srp.gensmo.utils.integration.trackEventOnSentry
import timber.log.Timber
import java.io.ByteArrayOutputStream
import java.io.File
import javax.inject.Inject

sealed class SearchEvent {
    data class Created(val taskId: String) : SearchEvent()
    data object NavigateToLogin : SearchEvent()
}

@HiltViewModel
class CollageSearchViewModel @Inject constructor(
) : ViewModel() {
    private var startTime: Long = 0

    private var _remixProduct: ProductItem? = null

    fun updateRemixProduct(product: ProductItem?) {
        _remixProduct = product
    }

    private val _query = MutableStateFlow("")
    val query: StateFlow<String> = _query.asStateFlow()
    fun updateQuery(query: String) {
        viewModelScope.launch {
            _query.emit(query)
        }
    }

    private var _budget: String? = null
    fun updateBudget(budget: String? = null) {
        _budget = budget
    }

    private var _stylesList: String? = null
    fun updateStylesList(stylesList: String? = null) {
        _stylesList = stylesList
    }

    private val _imageURI = MutableStateFlow<String?>(null)
    val imageURI: StateFlow<String?> = _imageURI.asStateFlow()
    fun updateImageURI(imageURI: String? = null) {
        viewModelScope.launch {
            _imageURI.emit(imageURI)
        }
    }

    private val _inspo = MutableStateFlow<List<SearchInspoItem>?>(null)
    val inspo: StateFlow<List<SearchInspoItem>?> = _inspo.asStateFlow()
    fun updateInspo(inspo: List<SearchInspoItem>? = null) {
        viewModelScope.launch {
            _inspo.emit(inspo)
        }
    }

    private val _usePreference = MutableStateFlow(false)
    val usePreference: StateFlow<Boolean> = _usePreference.asStateFlow()
    fun updateUsePreference(usePreference: Boolean) {
        viewModelScope.launch {
            _usePreference.emit(usePreference)
        }
    }

    private val _searchStatus = MutableStateFlow(TaskStatus.Idle)
    val searchStatus: StateFlow<TaskStatus> = _searchStatus.asStateFlow()

    private val _searchResult = MutableStateFlow<SearchItem?>(null)
    val searchResult: StateFlow<SearchItem?> = _searchResult.asStateFlow()

    private val _searchFlow = MutableSharedFlow<SearchEvent>()
    val searchFlow = _searchFlow.asSharedFlow()

    private val _processingText = MutableStateFlow<String?>(null)
    val processingText: StateFlow<String?> = _processingText.asStateFlow()

    var searchJob: Job? = null
    fun search() {
        searchJob?.cancel()
        searchJob = null
        searchJob = viewModelScope.launch {
            _searchStatus.emit(TaskStatus.Loading)
            try {
                // 声明变量用于存储上传后的图片URL
                var uploadedImageUrl: String? = null

                // 如果有图片URI，先上传图片
                if (!_imageURI.value.isNullOrEmpty()) {
                    // 检查是否已经是URL格式
                    if (_imageURI.value!!.startsWith("http://") || _imageURI.value!!.startsWith("https://")) {
                        // 如果已经是URL格式，直接使用该URL
                        uploadedImageUrl = _imageURI.value
                    } else {
                        // 如果不是URL格式，则进行上传处理
                        val uri = _imageURI.value!!.toUri()
                        val file = if (uri.scheme == "file") {
                            File(uri.path!!)
                        } else {
                            File(_imageURI.value!!)
                        }

                        try {
                            // 记录图片上传开始时间
                            val uploadStartTime = System.currentTimeMillis()
                            trackEventOnSentry("network_request", mapOf("path" to "/upload_image"))

                            // 获取图片宽高
                            val options = android.graphics.BitmapFactory.Options().apply {
                                inJustDecodeBounds = true
                            }
                            android.graphics.BitmapFactory.decodeFile(file.absolutePath, options)
                            val width = options.outWidth
                            val height = options.outHeight

                            // 获取预签名URL
                            val presignedResponse = ImageService.api.getPresignedUrl(
                                purpose = "user_upload", width = width, height = height
                            )

                            if (!presignedResponse.isSuccessful) {
                                Timber.e(
                                    "获取预签名URL失败: ${
                                        presignedResponse.errorBody()?.string()
                                    }"
                                )
                                _searchStatus.emit(TaskStatus.Fail)
                                trackEventOnSentry(
                                    "network_failure", mapOf(
                                        "path" to "/upload_image", "error" to "获取预签名URL失败"
                                    )
                                )
                                return@launch
                            }

                            val presignedData = presignedResponse.body() ?: run {
                                Timber.e("预签名数据为空")
                                _searchStatus.emit(TaskStatus.Fail)
                                trackEventOnSentry(
                                    "network_failure", mapOf(
                                        "path" to "/upload_image", "error" to "预签名数据为空"
                                    )
                                )
                                return@launch
                            }

                            // 使用预签名URL上传图片
                            val uploadSuccess = ImageService.uploadWithPresignedUrl(
                                presignedUrl = presignedData.presignedUrl, file = file
                            )

                            if (!uploadSuccess) {
                                Timber.e("使用预签名URL上传图片失败")
                                _searchStatus.emit(TaskStatus.Fail)
                                trackEventOnSentry(
                                    "network_failure", mapOf(
                                        "path" to "/upload_image",
                                        "error" to "使用预签名URL上传图片失败"
                                    )
                                )
                                return@launch
                            }

                            // 保存上传后的图片URL
                            uploadedImageUrl = presignedData.publicUrl

                            // 记录图片上传成功及耗时
                            val uploadDuration =
                                (System.currentTimeMillis() - uploadStartTime) / 1000.0
                            Timber.d("图片上传统计 - path: /upload_image, duration: ${uploadDuration}秒")
                            trackEventOnSentry(
                                "network_timing", mapOf(
                                    "path" to "/upload_image", "custom_duration" to uploadDuration
                                )
                            )
                        } catch (e: Exception) {
                            Timber.e(e, "上传图片过程中发生错误")
                            _searchStatus.emit(TaskStatus.Fail)
                            trackEventOnSentry(
                                "network_failure", mapOf(
                                    "path" to "/upload_image", "error" to (e.message ?: "未知错误")
                                )
                            )
                            return@launch
                        }
                    }
                }

                // 统一创建搜索任务的逻辑
                try {
                    Timber.d("query: ${_query.value}")
                    Timber.d("imageUrl: $uploadedImageUrl")

                    viewModelScope.launch {
                        getInspoList(_query.value, uploadedImageUrl)
                    }

                    startTime = System.currentTimeMillis()
                    trackEventOnSentry("network_request", mapOf("path" to "/search"))

                    try {
                        val searchRes = MoodboardService.api.createSearchTaskV2(
                            SearchV2Request(
                                isAsync = true,
                                query = _query.value,
                                imageUrl = uploadedImageUrl,
                                budget = _budget?.takeIf { it.isNotEmpty() } ?: "0",
                                inspoLabel = (_stylesList?.split(",")?.map { it.trim() }
                                    ?.filter { it.isNotEmpty() } ?: emptyList()),
                                specifiedProduct = _remixProduct
                            )
                        )

                        when {
                            searchRes.isSuccessful -> {
                                searchRes.body()?.let {
                                    _usePreference.emit(it.useUserPreference == true)
                                    pollTask(it.taskId)
                                } ?: run {
                                    _searchStatus.emit(TaskStatus.Fail)
                                    trackEventOnSentry(
                                        "network_failure", mapOf(
                                            "path" to "/search",
                                            "error" to "fail to create search task by server"
                                        )
                                    )
                                }
                            }

                            searchRes.code() == 401 -> {
                                _searchFlow.emit(SearchEvent.NavigateToLogin)
                                trackEventOnSentry(
                                    "network_failure", mapOf(
                                        "path" to "/search", "error" to "user not login"
                                    )
                                )
                            }

                            else -> {
                                _searchStatus.emit(TaskStatus.Fail)
                                trackEventOnSentry(
                                    "network_failure", mapOf(
                                        "path" to "/search",
                                        "error" to "fail to create search task by server"
                                    )
                                )
                            }
                        }
                    } catch (e: Exception) {
                        Timber.e(e, "创建搜索任务网络请求失败")
                        _searchStatus.emit(TaskStatus.Fail)
                        trackEventOnSentry(
                            "network_failure", mapOf(
                                "path" to "/search", "error" to (e.message ?: "未知错误")
                            )
                        )
                    }
                } catch (e: Exception) {
                    Timber.e(e, "创建搜索任务失败")
                    _searchStatus.emit(TaskStatus.Fail)
                    trackEventOnSentry(
                        "network_failure", mapOf(
                            "path" to "/search", "error" to (e.message ?: "未知错误")
                        )
                    )
                }
            } catch (err: Exception) {
                _searchStatus.emit(TaskStatus.Fail)
                trackEventOnSentry(
                    "network_failure", mapOf(
                        "path" to "/search", "error" to (err.message ?: "未知错误")
                    )
                )
            }
        }
    }

    private var pollingJob: Job? = null
    private val pollingInterval = 2000L
    private val maxAttempts = 30

    fun pollTask(taskId: String) {
        if (pollingJob?.isActive == true) return

        pollingJob = viewModelScope.launch {
            var attempt = 0
            while (attempt < maxAttempts) {
                try {
                    val res = MoodboardService.api.getSearchTask(taskId)
                    if (res.isSuccessful) {
                        val body = res.body()
                        if (body != null) {
                            when (body.status) {
                                "completed" -> {
                                    body.moodboards?.forEach {
                                        it.parsedContent =
                                            JSON.decodeFromString<MoodboardContent>(it.content)

                                        viewModelScope.launch {
                                            checkSaved(it.id)
                                        }
                                    }
                                    body.query?.let { _query.emit(it) }
                                    body.imageUrl?.let { _imageURI.emit(it) }
                                    _searchResult.emit(body)
                                    _searchStatus.emit(TaskStatus.Success)
                                    _searchFlow.emit(SearchEvent.Created(body.taskId))
                                    val duration = (System.currentTimeMillis() - startTime) / 1000.0
                                    Timber.d("网络请求统计 - path: /search, duration: ${duration}秒")
                                    trackEventOnSentry(
                                        "network_timing", mapOf(
                                            "path" to "/search", "custom_duration" to duration
                                        )
                                    )
                                    break
                                }

                                "failure" -> {
                                    _searchStatus.emit(TaskStatus.Fail)
                                    trackEventOnSentry(
                                        "network_failure", mapOf(
                                            "path" to "/search", "error" to "failure by server"
                                        )
                                    )
                                    break
                                }

                                "processing" -> {
                                    Timber.d("搜索任务正在处理中: ${body.displayText}")
                                    body.displayText?.let { text ->
                                        _processingText.emit(text)
                                    }
                                }

                                "qr_completed" -> {
                                    Timber.d("搜索任务已完成: ${body.displayText}")
                                    body.displayText?.let { text ->
                                        _processingText.emit(text)
                                    }
                                }

                                else -> {}
                            }
                        }
                    } else {
                        _searchStatus.emit(TaskStatus.Fail)
                        trackEventOnSentry(
                            "network_failure", mapOf(
                                "path" to "/search", "error" to "fail to search by server"
                            )
                        )
                        break
                    }
                } catch (e: Exception) {
                    _searchStatus.emit(TaskStatus.Fail)
                    trackEventOnSentry(
                        "network_failure", mapOf(
                            "path" to "/search", "error" to (e.message ?: "未知错误")
                        )
                    )
                    break
                }
                attempt++
                delay(pollingInterval)
            }

            if (attempt >= maxAttempts) {
                _searchStatus.emit(TaskStatus.Timeout)
                trackEventOnSentry(
                    "network_failure", mapOf(
                        "path" to "/search", "error" to "timeout"
                    )
                )
            }
        }
    }

    private fun stopPollingTask() {
        pollingJob?.cancel()
        pollingJob = null
    }

    override fun onCleared() {
        super.onCleared()
        stopPollingTask()
    }

    private suspend fun segmentImage(url: String? = null): String {
        if (url == null) return ""

        var link = ""
        val res = ImageService.api.segmentImage(listOf(url))
        if (res.isSuccessful) {
            res.body()?.let {
                link = it[url]?.url ?: ""
            }
        }

        return link
    }

    fun replaceBlock(
        moodboard: MoodboardEntity,
        original: ProductItem,
        target: ProductItem,
    ) {
        viewModelScope.launch {
            val replacedLink = segmentImage(target.mainImage?.link)

            val oriResult = _searchResult.value ?: return@launch

            val mutableProductMap = oriResult.searchProductList?.toMutableMap()

            mutableProductMap?.let { productMap ->
                val productList = productMap[original.id]?.toMutableList() ?: mutableListOf()
                if (productList.find { it.globalId == target.globalId } == null) {
                    productList.add(0, target)
                }
                mutableProductMap[original.id] = productList
            }

            val mutableMoodboards = oriResult.moodboards?.toMutableList() ?: mutableListOf()
            val moodboardIndex = mutableMoodboards.indexOfFirst { it.id == moodboard.id }
            val oriMoodboard = mutableMoodboards[moodboardIndex]

            val mutableProducts = oriMoodboard.products.toMutableList()
            val productIndex = mutableProducts.indexOfFirst { it.id == original.id }
            if (productIndex != -1) mutableProducts[productIndex] = target

            val mutableBlocks =
                oriMoodboard.parsedContent?.blocks?.toMutableList() ?: mutableListOf()
            val blockIndex = mutableBlocks.indexOfFirst { it.id == original.id }
            if (blockIndex != -1) {
                val block = mutableBlocks[blockIndex]
                mutableBlocks[blockIndex] = block.copy(
                    globalId = target.globalId, content = block.content.copy(
                        link = target.link, content = replacedLink
                    )
                )
            }

            val replacedMoodboard = oriMoodboard.copy(
                products = mutableProducts, parsedContent = oriMoodboard.parsedContent?.copy(
                    blocks = mutableBlocks
                )
            )

            mutableMoodboards[moodboardIndex] = replacedMoodboard

            val replacedResult = oriResult.copy(
                searchProductList = mutableProductMap,
                moodboards = mutableMoodboards,
            )

            _searchResult.emit(replacedResult)
        }
    }

    private val _searchInspoStatus = MutableStateFlow(TaskStatus.Idle)
    val searchInspoStatus: StateFlow<TaskStatus> = _searchInspoStatus.asStateFlow()

    private val _searchInspoResult = MutableStateFlow<List<SearchInspoItem>?>(null)
    val searchInspoResult: StateFlow<List<SearchInspoItem>?> = _searchInspoResult.asStateFlow()

    fun getInspoList(query: String, imageUrl: String?) {
        viewModelScope.launch {
            _searchInspoStatus.emit(TaskStatus.Loading)

            try {
                val res = SuggestService.api.getInspoList(
                    query.toRequestBody("text/plain".toMediaTypeOrNull()), imageUrl
                )

                if (res.isSuccessful) {
                    res.body()?.let { body ->
                        _searchInspoResult.emit(body.queryExtensionList)
                        _searchInspoStatus.emit(TaskStatus.Success)
                    } ?: run {
                        Timber.e("Parse failed")
                        _searchInspoStatus.emit(TaskStatus.Fail)
                    }
                } else {
                    Timber.e("Fetch failed")
                    _searchInspoStatus.emit(TaskStatus.Fail)
                }
            } catch (err: Exception) {
                Timber.e(err)
                _searchInspoStatus.emit(TaskStatus.Fail)
            }
        }
    }

    val moodboardSaveStateMap = mutableStateMapOf<String, Boolean>()

    private fun checkSaved(moodboardId: String) {
        viewModelScope.launch {
            try {
                val res = UserService.api.checkUserCollection(moodboardId)
                if (res.isSuccessful) {
                    res.body()?.let {
                        moodboardSaveStateMap[moodboardId] = it
                    }
                }
            } catch (e: Exception) {
                Timber.e(e, "检查收藏状态失败: $moodboardId")
                // 可以设置默认值或稍后重试
                moodboardSaveStateMap[moodboardId] = false
            }
        }
    }

    fun setSaveState(moodboardId: String, save: Boolean) {
        moodboardSaveStateMap[moodboardId] = save

        viewModelScope.launch {
            val res = if (save) {
                UserService.api.saveUserCollection(moodboardId)
            } else {
                UserService.api.deleteUserCollection(moodboardId)
            }

            if (res.isSuccessful) {
                res.body()?.let {
                    if (it.status == "success") return@launch
                }
            }

            moodboardSaveStateMap[moodboardId] = !save
        }
    }

    suspend fun updatePreviewFromBitmap(item: MoodboardEntity, result: SearchItem, bitmap: Bitmap) {
        // 获取Bitmap的宽高
        val width = bitmap.width
        val height = bitmap.height

        // 将Bitmap转换为字节数组
        val outputStream = ByteArrayOutputStream()
        bitmap.compress(Bitmap.CompressFormat.PNG, 100, outputStream)
        val imageBytes = outputStream.toByteArray()

        try {
            // 获取预签名URL
            val presignedResponse = ImageService.api.getPresignedUrl(
                purpose = "shared_images", width = width, height = height
            )

            if (!presignedResponse.isSuccessful) {
                Timber.e("获取预签名URL失败: ${presignedResponse.errorBody()?.string()}")
                return
            }

            val presignedData = presignedResponse.body() ?: run {
                Timber.e("预签名数据为空")
                return
            }

            // 创建临时文件用于上传 - 使用系统临时目录而不是context
            val tempFile = File.createTempFile("upload_image", ".png")
            tempFile.writeBytes(imageBytes)
            tempFile.deleteOnExit() // 确保应用退出时删除文件

            // 使用预签名URL上传图片
            val uploadSuccess = ImageService.uploadWithPresignedUrl(
                presignedUrl = presignedData.presignedUrl, file = tempFile
            )

            if (!uploadSuccess) {
                Timber.e("使用预签名URL上传图片失败")
                // 尝试删除临时文件
                tempFile.delete()
                return
            }

            // 上传成功后，处理后续逻辑
            val imageUrl = presignedData.publicUrl
            val ori = JSON.encodeToString<SearchItem>(result.copy(moodboards = null))
            val proc = JSON.decodeFromString<PostSearchItem>(ori)
            val target = proc.copy(
                moodboardId = item.id,
                image = imageUrl,
                moodboards = item.copy(parsedContent = null)
            )

            MoodboardService.api.postMoodboard(target)

            // 删除临时文件
            tempFile.delete()
        } catch (e: Exception) {
            Timber.e(e, "上传图片过程中发生错误")
        }
    }

    fun clear() {
        stopPollingTask()
        viewModelScope.launch {
            _query.emit("")
            _imageURI.emit(null)
            _budget = null
            _stylesList = null
            _inspo.emit(null)
            _usePreference.emit(false)
            _searchStatus.emit(TaskStatus.Idle)
            _searchResult.emit(null)
            _searchInspoStatus.emit(TaskStatus.Idle)
            _searchInspoResult.emit(null)
            _processingText.emit(null)
            _remixProduct = null
        }
    }

    fun cancelSearch() {
        stopPollingTask()
        searchJob?.cancel()
        searchJob = null
        viewModelScope.launch {
            _searchStatus.emit(TaskStatus.Idle)
        }
    }
}
